<template>
  <view class="my-page">
    <!-- 自定义头部 -->
    <view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="header-left">
          <!-- 返回按钮 -->
          <view class="back-btn" @click="goBack">
            <text key="1" class="back-icon"
              ><IconFont name="left" size="16" color="#000"
            /></text>
          </view>
        </view>
        <view class="header-center">
          <text key="2" class="header-title">业务员助手</text>
        </view>
        <view class="header-right"></view>
      </view>
    </view>
    <!-- 用户信息头部 -->
    <view class="user-header">
      <view class="user-info">
        <view class="avatar-wrapper">
          <image
            v-if="url"
            class="nut-avatar"
            :src="url"
            mode="aspectFit"
          ></image>
          <image
            v-else
            class="nut-avatar"
            src="../../images/user_default.png"
            mode="aspectFit"
          ></image>
        </view>
        <view class="user-details">
          <view class="greeting"
            >{{ displayName
            }}<text v-if="userInfo?.roleName" class="role-text">{{
              userInfo?.roleName || ""
            }}</text></view
          >
          <view class="company">{{ userInfo?.companyName || "" }}</view>
        </view>
      </view>
    </view>

    <!-- 退出登录按钮 -->
    <view class="logout-section">
      <nut-button block size="large" class="logout-btn" @click="handleLogout">
        退出登录
      </nut-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import Taro, { useDidShow } from "@tarojs/taro";
import { useUserStore } from "@/store/modules/user";
import { useSelectedStore } from "@/store/modules/selected";
import { IconFont } from "@nutui/icons-vue-taro";

// 设置当前选中的 tab
const selectedStore = useSelectedStore();

const userStore = useUserStore();
// 状态栏高度
const statusBarHeight = ref(44);

// 获取系统信息
useDidShow(() => {
  selectedStore.setSelected(2);
  userStore.initFromStorage();
  try {
    Taro.getSystemInfo({
      success: (res) => {
        statusBarHeight.value = res.statusBarHeight || 44;
        console.log("系统信息:", res);
      },
      fail: (err) => {
        console.warn("获取系统信息失败:", err);
        statusBarHeight.value = 44;
      },
    });
  } catch (error) {
    console.warn("获取系统信息异常:", error);
    statusBarHeight.value = 44;
  }
});
// 返回上一页
const goBack = () => {
  selectedStore.setSelected(0);
  try {
    Taro.switchTab({
      url: "/pages/index/index",
    });
  } catch (error) {
    selectedStore.setSelected(2);
    console.warn("返回失败:", error);
  }
};
// 计算属性获取用户信息
const userInfo = computed(() => userStore.userInfo);
const displayName = computed(() => {
  if (userInfo.value?.nickname) {
    return `${userInfo.value.nickname}`;
  }
  if (userInfo.value?.username) {
    return `${userInfo.value.username}`;
  }
  return "";
});
const url = computed(() => userInfo.value?.avatar);
// 处理退出登录
const handleLogout = () => {
  Taro.showModal({
    title: "提示",
    content: "确定要退出登录吗？",
    success: (res) => {
      if (res.confirm) {
        // 清除用户信息
        userStore.logout();

        Taro.showToast({
          title: "已退出登录",
          icon: "none",
          duration: 400,
        });

        // 跳转到登录页面
        setTimeout(() => {
          Taro.redirectTo({
            url: "/subpackages/login/index",
          });
        }, 100);
      }
    },
  });
};
</script>

<style lang="less">
.my-page {
  min-height: 100vh;
  .custom-header {
    background: #fff !important;
    .header-content {
      .header-title {
        color: #2f3133 !important;
      }
    }
  }

  .user-header {
    padding: 15vh 32rpx 80rpx 32rpx;
    position: relative;

    .user-info {
      display: flex;
      align-items: center;

      .avatar-wrapper {
        margin-right: 32rpx;

        .nut-avatar {
          width: 140rpx;
          height: 140rpx;
          flex-shrink: 0;
          border-radius: 50%;
          border: 1px solid #fff;
        }
      }

      .user-details {
        flex: 1;

        .greeting {
          color: #ffffff;
          font-size: 36rpx;
          font-weight: 600;
          margin-bottom: 16rpx;
        }

        .role-text {
          background: #21c3c0;
          color: #ffffff;
          padding: 8rpx 16rpx;
          border-radius: 8rpx;
          font-size: 20rpx;
          display: inline-block;
          margin-left: 16rpx;
          font-weight: 400;
          font-family: OPPOSans;
        }

        .company {
          color: rgba(255, 255, 255, 0.9);
          font-size: 28rpx;
          line-height: 1.4;
          font-weight: 400;
        }
      }
    }
  }

  .logout-section {
    position: fixed;
    bottom: 200rpx;
    left: 50%;
    width: 95vw;
    transform: translateX(-50%);

    .logout-btn {
      border-radius: 16rpx;
      height: 88rpx;
      font-size: 28rpx;
      font-weight: 600;
      color: #1a43ad;
      border: none;
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
    }
  }
}
</style>
