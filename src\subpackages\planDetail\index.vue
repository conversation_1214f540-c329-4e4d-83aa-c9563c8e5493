<template>
  <view class="plan-detail-page">
    <!-- 头部信息 -->
    <view class="header-info">
      <view class="info-content">
        <view class="info-item">
          <view class="label1">到货计划编号</view>
          <view class="value1">{{ planInfo.planNumber }}</view>
        </view>
        <view class="info-item">
          <text class="label">供应商名称：</text>
          <text class="value">{{ planInfo.supplierName }}</text>
        </view>
        <view class="info-item">
          <text class="label">企业前缀：</text>
          <text class="value">{{ planInfo.companyPrefix }}</text>
        </view>
      </view>
      <view>
        <image
          style="width: 100rpx; height: 120rpx"
          src="../../../images/xq.png"
          mode="aspectFit"
        />
      </view>
    </view>

    <!-- 计划信息折叠面板 -->
    <view class="detail-section">
      <view class="section-title">
        <text>计划信息</text>
        <view
          class="collapse-arrow"
          :class="{ expanded: isPlanExpanded }"
          @click="togglePlan"
        >
          <IconFont name="rect-down" size="16" color="#666" />
        </view>
      </view>
      <view class="collapse-content" v-show="isPlanExpanded">
        <view class="shipping-info">
          <view class="info-item">
            <text class="info-label">关联采购单号</text>
            <text class="info-value">{{ planDetails.purchaseOrderNo }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">物料名称</text>
            <text class="info-value">{{ planDetails.materialName }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">物料编码</text>
            <text class="info-value">{{ planDetails.materialCode }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">计划数量</text>
            <text class="info-value">{{ planDetails.plannedQuantity }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">计划交货时间</text>
            <text class="info-value">{{ planDetails.deliveryDate }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">备注</text>
            <text class="info-value">{{ planDetails.remarks }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">补了 3000kg</text>
            <text class="info-value"></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 发货信息折叠面板 -->
    <view class="detail-section">
      <view class="section-title">
        <text>发货信息</text>
        <view
          class="collapse-arrow"
          :class="{ expanded: isShippingExpanded }"
          @click="toggleShipping"
        >
          <IconFont name="rect-down" size="16" color="#666" />
        </view>
      </view>
      <view class="collapse-content" v-show="isShippingExpanded">
        <view class="shipping-info">
          <view class="info-item">
            <text class="info-label">发货数量</text>
            <text class="info-value">{{ shippingDetails.quantity }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">生产批号</text>
            <text class="info-value">{{ shippingDetails.batchNumber }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">发货单号</text>
            <text class="info-value">{{ shippingDetails.shippingNumber }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">发货时间</text>
            <text class="info-value">{{ shippingDetails.shippingTime }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">运输类型</text>
            <text class="info-value">{{ shippingDetails.transportType }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">司机电话</text>
            <text class="info-value">{{ shippingDetails.driverPhone }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">车牌号</text>
            <text class="info-value">{{ shippingDetails.plateNumber }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">备注</text>
            <text class="info-value">{{ shippingDetails.remarks }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">补了 3000kg</text>
            <text class="info-value"></text>
          </view>
        </view>

        <!-- 图片区域 -->
        <view class="image-label">图片</view>
        <view class="image-gallery">
          <view
            class="image-item"
            v-for="(image, index) in shippingDetails.images"
            :key="index"
            @click="previewImage(image)"
          >
            <image :src="image" mode="aspectFill" class="gallery-image" />
          </view>
        </view>
      </view>
    </view>
    <!-- 收货信息折叠面板 -->
    <view class="detail-section">
      <view class="section-title">
        <text>收货信息</text>
        <view
          class="collapse-arrow"
          :class="{ expanded: isReceiving }"
          @click="toggleReceiving"
        >
          <IconFont name="rect-down" size="16" color="#666" />
        </view>
      </view>
      <view class="collapse-content" v-show="isReceiving">
        <view class="shipping-info">
          <view class="info-item">
            <text class="info-label">实收数量</text>
            <text class="info-value">{{ shippingDetails.quantity }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">备注</text>
            <text class="info-value">{{ shippingDetails.remarks }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">补了 3000kg</text>
            <text class="info-value"></text>
          </view>
        </view>

        <!-- 图片区域 -->
        <view class="image-label">图片</view>
        <view class="image-gallery">
          <view
            class="image-item"
            v-for="(image, index) in shippingDetails.images"
            :key="index"
            @click="previewImage(image)"
          >
            <image :src="image" mode="aspectFill" class="gallery-image" />
          </view>
        </view>
      </view>
    </view>
    <!-- 收货作业表单 -->
    <view class="shipping-section">
      <view class="section-title">
        <text>收货作业</text>
      </view>
      <view class="shipping-info">
        <!-- 实收数量 - 必填 -->
        <view class="info-item">
          <text class="info-label required">实收数量</text>
          <view class="input-area">
            <input
              v-model="receivingForm.actualQuantity"
              type="number"
              placeholder="请输入"
              class="quantity-input"
              @input="onQuantityInput"
              @blur="onQuantityBlur"
            />
          </view>
        </view>
        <view class="error-text" v-if="errors.actualQuantity">
          {{ errors.actualQuantity }}
        </view>

        <!-- 收货备注开关 -->
        <view class="info-item">
          <text class="info-label">收货备注</text>
          <view class="switch-container">
            <text class="switch-status">{{
              receivingForm.remarksEnabled ? "备注给对方" : ""
            }}</text>
            <nut-switch
              v-model="receivingForm.remarksEnabled"
              size="small"
              active-color="#52c41a"
            />
          </view>
        </view>

        <!-- 备注输入框 -->
        <view class="info-item">
          <text class="info-label">备注内容</text>
          <nut-textarea
            v-model="receivingForm.remarks"
            placeholder="请输入备注"
            :rows="4"
            :max-length="200"
            show-word-limit
            class="info-textarea"
          />
        </view>
      </view>

      <!-- 图片上传 -->
      <view class="image-label">图片</view>
      <view class="image-gallery">
        <nut-uploader
          v-model:file-list="fileList"
          :maximum="9"
          multiple
          :preview-type="'picture'"
          @start="onStart"
          @progress="onProgress"
          @oversize="onOversize"
          @success="onSuccess"
          @failure="onFailure"
          @change="onChange"
          @delete="onDelete"
        >
          <view class="upload-button">
            <IconFont name="photograph" size="40" color="#ccc" />
          </view>
        </nut-uploader>
      </view>
    </view>

    <!-- 确认收货按钮 -->
    <view class="confirm-section">
      <nut-button
        type="success"
        size="large"
        block
        :loading="confirmLoading"
        @click="confirmReceiving"
        class="confirm-btn"
      >
        确认收货
      </nut-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import Taro from "@tarojs/taro";
import { IconFont } from "@nutui/icons-vue-taro";

// 页面数据
const planInfo = reactive({
  planNumber: "JH0088613270000001",
  supplierName: "江苏康缘生态农业发展有限公司",
  companyPrefix: "88.588.10086",
});

const planDetails = reactive({
  purchaseOrderNo: "4100002386",
  materialName: "金银花",
  materialCode: "9617244",
  plannedQuantity: "20000kg",
  deliveryDate: "2025-05-14",
  remarks: "备注给对方",
});

const shippingDetails = reactive({
  quantity: "22000kg",
  batchNumber: "cp202405",
  shippingNumber: "FH0088613270000001",
  shippingTime: "2025-05-14  09:28:31",
  transportType: "三方专线",
  driverPhone: "13813878023",
  plateNumber: "苏A3680Q",
  remarks: "对方备注给你",
  images: [
    "https://via.placeholder.com/200x150/4A90E2/FFFFFF?text=Image1",
    "https://via.placeholder.com/200x150/F5A623/FFFFFF?text=Image2",
  ],
});

const receivingForm = reactive({
  actualQuantity: "",
  remarksEnabled: false,
  remarks: "",
});

// 状态管理
const isPlanExpanded = ref(true);
const isShippingExpanded = ref(true);
const isReceiving = ref(true);
const confirmLoading = ref(false);
const fileList = ref<any[]>([]);

// 表单验证错误
const errors = reactive({
  actualQuantity: "",
});

// 切换计划信息展开状态
const togglePlan = () => {
  isPlanExpanded.value = !isPlanExpanded.value;
};

// 切换发货信息展开状态
const toggleShipping = () => {
  isShippingExpanded.value = !isShippingExpanded.value;
};
const toggleReceiving = () => {
  isReceiving.value = !isReceiving.value;
};
// 数量输入处理方法
const onQuantityInput = () => {
  // 清除错误信息
  errors.actualQuantity = "";
};

const onQuantityBlur = () => {
  // 可以在这里添加失焦时的验证逻辑
  if (
    receivingForm.actualQuantity &&
    receivingForm.actualQuantity.toString().trim() === ""
  ) {
    receivingForm.actualQuantity = "";
  }
};

// 备注开关切换
// const onRemarksToggle = (value: boolean) => {
//   receivingForm.remarksEnabled = value;
//   if (!value) {
//     receivingForm.remarks = "";
//   }
// };

// 图片预览
const previewImage = (imageUrl: string) => {
  Taro.previewImage({
    current: imageUrl,
    urls: shippingDetails.images,
  });
};

// 表单验证
const validateForm = () => {
  errors.actualQuantity = "";

  if (!receivingForm.actualQuantity) {
    errors.actualQuantity = "实收数量不能为空";
    return false;
  }

  if (
    isNaN(Number(receivingForm.actualQuantity)) ||
    Number(receivingForm.actualQuantity) <= 0
  ) {
    errors.actualQuantity = "请输入有效的数量";
    return false;
  }

  return true;
};

// 确认收货
const confirmReceiving = async () => {
  if (!validateForm()) {
    return;
  }

  // 显示确认对话框
  const result = await Taro.showModal({
    title: "确认收货",
    content: "确定要确认收货吗？",
    confirmText: "确认",
    cancelText: "取消",
  });

  if (!result.confirm) {
    return;
  }

  confirmLoading.value = true;

  try {
    // 准备提交数据
    const submitData = {
      planNumber: planInfo.planNumber,
      actualQuantity: receivingForm.actualQuantity,
      remarks: receivingForm.remarksEnabled ? receivingForm.remarks : "",
      fileList: fileList.value,
    };

    console.log("提交数据:", submitData);

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 2000));

    Taro.showToast({
      title: "收货成功",
      icon: "none",
      duration: 2000,
    });

    // 返回上一页
    setTimeout(() => {
      Taro.navigateBack();
    }, 500);
  } catch (error) {
    console.error("收货失败:", error);
    Taro.showToast({
      title: "收货失败，请重试",
      icon: "none",
    });
  } finally {
    confirmLoading.value = false;
  }
};

// 文件上传相关方法
const onStart = () => {
  console.log("start 触发");
};

const onProgress = ({
  percentage,
}: {
  event: any;
  options: any;
  percentage: number;
}) => {
  console.log("progress 触发", percentage);
};

const onOversize = (files: any[]) => {
  console.log("oversize 触发 文件大小不能超过 50kb", files);
  Taro.showToast({
    title: "文件大小超出限制",
    icon: "none",
  });
};

const onSuccess = ({
  responseText,
  options,
}: {
  responseText: any;
  options: any;
}) => {
  console.log("success 触发", responseText, options);
  Taro.showToast({
    title: "上传成功",
    icon: "none",
  });
};

const onFailure = ({
  responseText,
  options,
}: {
  responseText: any;
  options: any;
}) => {
  console.log("failure 触发", responseText, options);
  Taro.showToast({
    title: "上传失败",
    icon: "none",
  });
};

const onChange = ({
  fileList: newFileList,
  event,
}: {
  fileList: any[];
  event: any;
}) => {
  console.log("change 触发", newFileList, event);
  fileList.value = newFileList;
};

const onDelete = ({
  file,
  fileList: newFileList,
  index,
}: {
  file: any;
  fileList: any[];
  index: number;
}) => {
  console.log("delete 触发", file, newFileList, index);
  fileList.value = newFileList;
};

onMounted(() => {
  // 页面初始化
  console.log("计划详情页面加载完成");
});
</script>

<style lang="less">
.plan-detail-page {
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 40rpx;

  .header-info {
    border-radius: 16rpx;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
    padding: 16rpx 24rpx 1rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .info-content {
      flex: 1;
    }

    .info-item {
      margin-bottom: 16rpx;

      .label {
        color: rgba(29, 29, 29, 0.6);
        font-style: normal;
        font-weight: 400;
      }
      .label1 {
        color: #2f3133;
        font-size: 32rpx;
        font-style: normal;
        font-weight: 600;
      }

      .value {
        color: #1d1d1d;
        font-style: normal;
        font-weight: 400;
      }
      .value1 {
        flex: 1;
        overflow-x: auto;
        color: #2f3133;
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 50rpx;
        border-bottom: 1px solid #eef1f5;
      }
    }
  }

  .detail-section,
  .shipping-section {
    border-radius: 16rpx;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    margin-top: 20rpx;
  }

  .section-title {
    color: #1a43ad;
    font-style: normal;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;
    font-size: 28rpx;
    border-bottom: 1px solid #eef1f5;

    .collapse-arrow {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transform: rotate(0deg);
      transition: transform 0.2s ease;

      &.expanded {
        transform: rotate(180deg);
      }
    }
  }

  .collapse-content {
    background: white;
  }

  .shipping-info {
    .info-item {
      display: flex;
      padding: 24rpx;
      border-bottom: 1px solid #eef1f5;

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        color: #2f3133;
        min-width: 160rpx;
        margin-right: 20rpx;

        &.required::before {
          content: "*";
          color: #ff4d4f;
          margin-right: 4rpx;
        }
      }

      .info-textarea {
        flex: 1;
        border-radius: 16rpx;
        background: #f5f7fa;
        height: 180rpx;
      }

      .info-value {
        color: #8d9094;
        flex: 1;
        line-height: 1.5;
        text-align: right;
      }

      .input-area {
        display: flex;
        align-items: center;
        flex: 1;
        justify-content: flex-end;
        gap: 16rpx;

        .quantity-input {
          font-size: 28rpx;
          color: #2f3133;
          text-align: right;
          border: none;
          background: transparent;
          outline: none;
          width: 200rpx;

          &::placeholder {
            color: #8d9094;
            font-size: 28rpx;
          }
        }
      }

      .switch-container {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .switch-status {
          font-size: 24rpx;
          color: #52c41a;
        }
      }
    }

    .error-text {
      font-size: 24rpx;
      color: #ff4d4f;
      margin: 8rpx 24rpx;
    }
  }

  .image-label {
    color: #2f3133;
    min-width: 160rpx;
    margin-left: 24rpx;
    padding-top: 24rpx;
  }

  .image-gallery {
    padding: 24rpx;
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;

    .image-item {
      width: 128rpx;
      height: 128rpx;
      border-radius: 12rpx;
      overflow: hidden;

      .gallery-image {
        width: 100%;
        height: 100%;
        border-radius: 12rpx;
      }
    }

    .upload-button {
      width: 128rpx;
      height: 128rpx;
      border: 2rpx dashed #ddd;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fafafa;

      &:active {
        background-color: #f0f0f0;
      }
    }
  }

  .confirm-section {
    margin-top: 40rpx;
    padding: 0 20rpx 40rpx;

    .confirm-btn {
      height: 88rpx !important;
      border-radius: 44rpx !important;
      background: linear-gradient(180deg, #62f8ff 0%, #091899 100%),
        linear-gradient(180deg, #ffbf1a -33.33%, #e55f14 131.94%) !important;
      box-shadow: 1px 1px 6.1px 0px rgba(243, 198, 106, 0.3) !important;
      color: #fff !important;
      font-size: 28rpx !important;
      font-style: normal !important;
      font-weight: 600 !important;
      border: none !important;
    }
  }
}
</style>
