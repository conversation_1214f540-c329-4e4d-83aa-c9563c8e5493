<template>
  <view class="shipment-edit">
    <!-- 顶部信息 -->
    <view class="header-info">
      <view class="info-content">
        <view class="info-item">
          <view class="label1">发货单号：</view>
          <view class="value1">{{ shipmentInfo.deliveryOrderNo }}</view>
        </view>
        <view class="info-item">
          <text class="label">客户名称：</text>
          <text class="value">{{ shipmentInfo.customerName }}</text>
        </view>
        <view class="info-item">
          <text class="label">企业前缀：</text>
          <text class="value">{{ shipmentInfo.customerPrefix }}</text>
        </view>
      </view>
      <view>
        <image
          style="width: 100rpx; height: 120rpx"
          src="../../../images/xq.png"
          mode="aspectFit"
        >
        </image>
      </view>
    </view>

    <!-- 明细信息 -->
    <view class="detail-section">
      <view class="section-title">
        <text>明细信息</text>
        <view class="add-btn" @tap="handleAddItem">
          <image
            style="width: 28rpx; height: 28rpx"
            src="../../../images/jia.png"
            mode="aspectFit"
          >
          </image>
        </view>
      </view>

      <view class="detail-list" :key="forceUpdateKey">
        <nut-swipe
          v-for="(item, index) in detailList"
          :key="`swipe-${item.planNo || item.id || index}-${forceUpdateKey}`"
          :name="`${index}`"
          @open="open"
          @close="close"
          ref="swipeRefs"
        >
          <view class="detail-item" :class="{ highlight: item.highlight }">
            <view class="item-content">
              <view class="item-header">
                <view
                  class="item-status urgent"
                  v-if="
                    item.deliveryOrderHerbInfoVOList.every(
                      (item) => !item.actualQuantity
                    )
                  "
                  >未填报</view
                >
                <text class="plan-number">计划编号：{{ item.planNo }}</text>
              </view>
              <view class="content-row">
                <text class="content-label">物料名称：</text>
                <text class="content-value">{{ item.materialName }}</text>
              </view>
              <nut-row style="margin-bottom: 10rpx">
                <nut-col :span="13">
                  <view class="content-row">
                    <text class="content-label">明细行数：</text>
                    <text class="content-value">{{
                      item.deliveryOrderHerbInfoVOList.filter(
                        (item) => item.identificationCode
                      ).length || 0
                    }}</text>
                  </view>
                </nut-col>
                <nut-col :span="11">
                  <view class="content-row">
                    <text class="content-label">计划数量：</text>
                    <text class="content-value"
                      >{{ item.planQuantity }}{{ item.unitName }}</text
                    >
                  </view>
                </nut-col>
              </nut-row>
              <nut-row>
                <nut-col :span="13">
                  <view class="content-row">
                    <text class="content-label">计划交货时间：</text>
                    <text class="content-value">{{
                      item.planDeliveryTime
                    }}</text>
                  </view>
                </nut-col>
                <nut-col :span="11">
                  <view class="content-row">
                    <text class="content-label">实发数量：</text>
                    <text class="content-value"
                      >{{ calculateActualQuantity(item)
                      }}{{ item.unitName }}</text
                    >
                  </view>
                </nut-col>
              </nut-row>
            </view>
            <view @click="handleEdit(item)">
              <image
                style="width: 32rpx; height: 32rpx"
                src="../../../images/edit.png"
                mode="aspectFit"
              >
              </image>
            </view>
          </view>
          <template #right>
            <view class="delete-button" @tap="handleDelete(index)">删除</view>
          </template>
        </nut-swipe>
      </view>
    </view>

    <!-- 发货信息 -->
    <view class="shipping-section">
      <view class="section-title">发货信息</view>

      <view class="shipping-info">
        <!-- 收货地址 -->
        <view class="info-item" @tap="handleSelectAddress">
          <text class="info-label">收货地址</text>
          <view class="info-value-container">
            <text class="info-value">{{
              shipmentInfo.deliveryAddress || "请选择收货地址"
            }}</text>
            <IconFont name="right" size="16" color="#31373D"></IconFont>
          </view>
        </view>

        <!-- 运输类型 -->
        <view class="info-item" @tap="handleSelectTransport">
          <text class="info-label">运输类型</text>
          <view class="info-value-container">
            <text class="info-value">{{
              transportTypeObj[shipmentInfo.transportType] || "请选择运输类型"
            }}</text>
            <IconFont name="right" size="16" color="#31373D"></IconFont>
          </view>
        </view>

        <!-- 司机电话 -->
        <view class="info-item">
          <text class="info-label">司机电话</text>
          <view class="info-value-container">
            <input
              class="info-input"
              v-model="shipmentInfo.driverPhone"
              placeholder="请输入"
              type="tel"
            />
            <IconFont name="right" size="16" color="#31373D"></IconFont>
          </view>
        </view>

        <!-- 车牌号 -->
        <view class="info-item">
          <text class="info-label">车牌号</text>
          <view class="info-value-container">
            <input
              class="info-input"
              v-model="shipmentInfo.licensePlate"
              placeholder="请输入"
            />
            <IconFont name="right" size="16" color="#31373D"></IconFont>
          </view>
        </view>

        <!-- 发货备注开关 -->
        <view class="info-item switch-item">
          <text class="info-label">发货备注</text>
          <view class="switch-container">
            <text>备注给对方</text>
            <nut-switch
              active-color="#1A43AD"
              size="small"
              v-model="shipmentInfo.deliveryRemarkEnabled"
            />
          </view>
        </view>
      </view>

      <!-- 备注输入框 -->
      <view class="remark-section">
        <textarea
          class="remark-textarea"
          v-model="shipmentInfo.deliveryRemark"
          placeholder="请填写发货备注内容"
          maxlength="200"
          :show-count="false"
        />
      </view>

      <!-- 图片上传 -->
      <view class="image-section">
        <view class="image-label">发货图片</view>
        <view class="image-gallery">
          <ImageUploader
            v-model="deliveryImages"
            :maximum="3"
            :max-size="4"
            @upload-success="onUploadSuccess"
            @upload-error="onUploadError"
            @delete="onImageDelete"
          />
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-button">
      <nut-button type="success" block @click="handleConfirm"
        >确认发货</nut-button
      >
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed, nextTick } from "vue";
import Taro, { useRouter, useDidShow } from "@tarojs/taro";
import { IconFont } from "@nutui/icons-vue-taro";
import {
  queryDeliveryOrderDetail,
  queryDeliveryPlanAll,
  updateDeliveryOrderApi,
} from "@/api/shipment";
import { useShipmentStore } from "@/store/modules/shipment";
import ImageUploader from "../../../components/ImageUploader/index.vue";

const shipmentStore = useShipmentStore();
// 路由参数
const router = useRouter();
// 发货单信息 - 基于API字段结构
const shipmentInfo = reactive({
  deliveryOrderNo: "",
  customerName: "",
  customerCreditCode: "",
  customerPrefix: "",
  deliveryAddress: "",
  transportType: "",
  driverPhone: "",
  licensePlate: "",
  deliveryRemark: "",
  deliveryRemarkEnabled: false,
});
const deliveryAddressOptions = ref<string[]>([]);
const userInfo = JSON.parse(Taro.getStorageSync("userInfo"));
const queryPlanAll = async (planIds: any) => {
  try {
    const response = await queryDeliveryPlanAll({ planIds });
    deliveryAddressOptions.value = response.data.map(
      (item) => item.receiveAddress
    );
  } catch (error) {
    console.error("获取可选计划列表失败:", error);
  }
};
// 明细列表 - 使用计算属性创建响应式副本，确保组件正确重新渲染
const detailList = computed(() => [...shipmentStore.detailList]);

// 强制刷新key，用于解决nut-swipe左滑失效问题
const forceUpdateKey = ref(0);

// swipe组件引用
const swipeRefs = ref<any[]>([]);

// 发货图片列表
const deliveryImages = ref<string[]>([]);

const images = ref<string[]>([]);

// 页面加载状态
const loading = ref(false);

// 滑动操作
const open = (obj: any) => {
  console.log("打开", obj);
};

const close = (obj: any) => {
  console.log("关闭", obj);
};

// 重置所有swipe组件状态
const resetSwipeStates = () => {
  nextTick(() => {
    if (swipeRefs.value && swipeRefs.value.length > 0) {
      swipeRefs.value.forEach((swipeRef: any) => {
        if (swipeRef && typeof swipeRef.close === "function") {
          swipeRef.close();
        }
      });
    }
  });
};

// 计算实发数量总和
const calculateActualQuantity = (item: any) => {
  if (
    !item.deliveryOrderHerbInfoVOList ||
    !Array.isArray(item.deliveryOrderHerbInfoVOList)
  ) {
    return 0;
  }

  return item.deliveryOrderHerbInfoVOList.reduce(
    (total: number, herbInfo: any) => {
      const actualQuantity = parseFloat(herbInfo.actualQuantity) || 0;
      return total + actualQuantity;
    },
    0
  );
};

// 添加明细项
const handleAddItem = () => {
  Taro.showLoading({ title: "加载中" });

  // 模拟API请求获取可添加的明细项
  setTimeout(() => {
    Taro.hideLoading();

    // 打开选择明细项的页面或弹窗
    Taro.navigateTo({
      url: `/subpackages/send/selectPlan/index?customerName=${shipmentInfo.customerName}&enterpriseCode=${shipmentInfo.customerPrefix}&customerEntCode=${shipmentInfo.customerCreditCode}`,
    });
  }, 200);
};

// 删除明细项
const handleDelete = (index: number) => {
  Taro.showModal({
    title: "提示",
    content: "确定要删除该明细项吗？",
    success: (res) => {
      if (res.confirm) {
        shipmentStore.removeDetailItem(index);
        Taro.showToast({
          title: "删除成功",
          icon: "none",
        });
      }
    },
  });
};

// 选择地址
const handleSelectAddress = () => {
  console.log("handleSelectAddress", deliveryAddressOptions.value);
  if (
    deliveryAddressOptions.value.length === 1 &&
    !deliveryAddressOptions.value[0]
  ) {
    return Taro.showToast({
      title: "暂无可选收货地址",
      icon: "none",
    });
  }
  Taro.showActionSheet({
    itemList: deliveryAddressOptions.value,
    success: (res) => {
      const types = deliveryAddressOptions.value;
      shipmentInfo.deliveryAddress = types[res.tapIndex];
    },
  });
};
const transportTypeObj: any = ref({
  "1": "自有车辆",
  "2": "第三方车辆",
});
// 选择运输类型
const handleSelectTransport = () => {
  Taro.showActionSheet({
    itemList: Object.values(transportTypeObj.value),
    success: (res) => {
      console.log("选择运输类型:", res);
      shipmentInfo.transportType = res.tapIndex + 1 + "";
    },
  });
};
// 图片上传相关事件处理
const onUploadSuccess = (data: any) => {
  console.log("图片上传成功", data);
  // 将上传成功的图片添加到deliveryImages中
  images.value.push(data.data.url);
};

const onUploadError = (error: any) => {
  console.log("图片上传失败", error);
};

const onImageDelete = (file: any) => {
  console.log("图片删除", file);
};
// 预览图片
// const previewImage = (current: string) => {
//   Taro.previewImage({
//     current,
//     urls: deliveryImages.value,
//   });
// };

// 确认发货
const handleConfirm = async () => {
  console.log(deliveryImages.value, "deliveryImages");

  // 表单验证
  // if (!shipmentInfo.deliveryAddress) {
  //   return Taro.showToast({
  //     title: "请选择收货地址",
  //     icon: "none",
  //   });
  // }

  // if (!shipmentInfo.transportType) {
  //   return Taro.showToast({
  //     title: "请选择运输类型",
  //     icon: "none",
  //   });
  // }

  // 检查明细项是否都有实发数量
  let arr: boolean[] = [];
  shipmentStore.detailList.forEach((item: any) => {
    arr.push(
      (item.deliveryOrderHerbInfoVOList as any[]).every(
        (detail: any) => !detail.actualQuantity
      )
    );
  });
  const invalidItem = arr.some((v) => v);
  if (invalidItem) {
    return Taro.showToast({
      title: "请填写所有明细项的实发数量",
      icon: "none",
    });
  }

  // 提交表单
  Taro.showLoading({ title: "提交中" });
  const tmp = {
    ...shipmentInfo,
    deliveryImages: images.value,
    details: shipmentStore.detailList.map((item: any) => ({
      ...item,
      deliveryOrderHerbInfoVOList: item.deliveryOrderHerbInfoVOList.filter(
        (detail: any) => detail.identificationCode
      ),
    })),
    source: "2",
    type: "2",
    userId: userInfo.userId,
    deliveryRemarkEnabled: shipmentInfo.deliveryRemarkEnabled ? "1" : "0",
  };
  console.log(tmp, "tmp");

  const response = await updateDeliveryOrderApi(tmp);
  if (response.success) {
    Taro.hideLoading();
    shipmentStore.resetDetailList();
    Taro.showToast({
      title: "发货成功",
      icon: "none",
      duration: 2000,
      success: () => {
        // 延迟返回上一页
        setTimeout(() => {
          Taro.reLaunch({ url: "/subpackages/send/shipOperation/index" });
        }, 500);
      },
    });
  }
};
const handleEdit = (item: any) => {
  Taro.navigateTo({
    url: `/subpackages/send/supplyDetail/index?planNo=${item.planNo}&customerName=${shipmentInfo.customerName}&enterpriseCode=${shipmentInfo.customerPrefix}&enterpriseCode=${shipmentInfo.customerPrefix}&deliveryOrderNo=${item.deliveryOrderNo}`,
  });
};
// 获取发货单详情
const fetchDeliveryOrderDetail = async (deliveryOrderNo: string) => {
  try {
    loading.value = true;
    Taro.showLoading({ title: "加载中..." });

    const response = await queryDeliveryOrderDetail({ deliveryOrderNo });

    if (response && response.success && response.data) {
      const data = response.data;

      // 更新发货单信息
      Object.assign(shipmentInfo, {
        deliveryOrderNo: data.deliveryOrderNo || "",
        customerName: data.customerName || "",
        customerCreditCode: data.customerCreditCode || "",
        customerPrefix: data.customerPrefix || "",
        deliveryAddress: data.deliveryAddress || "",
        transportType: data.transportType || "",
        driverPhone: data.driverPhone || "",
        licensePlate: data.licensePlate || "",
        deliveryRemark: data.deliveryRemark || "",
        deliveryRemarkEnabled: data.deliveryRemarkEnabled === 1 ? true : false,
      });

      // 更新明细列表
      shipmentStore.setDetailList(data.details || []);

      // 强制刷新swipe组件
      forceUpdateKey.value += 1;

      // 更新图片列表
      deliveryImages.value =
        data.deliveryImages &&
        data.deliveryImages.map((item) => {
          return {
            url: item,
            name: item,
            status: "success",
            uid: item,
            type: "image/jpeg",
          };
        });
      images.value = data.deliveryImages || [];
      shipmentStore.selectedIds = data.planIds || [];
      shipmentStore.selectedPlanNos = data.details.map((item) => item.planNo);
      console.log("发货单详情加载成功:", data);
    } else {
      throw new Error(response?.message || "获取发货单详情失败");
    }
  } catch (error) {
    console.error("获取发货单详情失败:", error);
    Taro.showToast({
      title: "获取详情失败",
      icon: "none",
    });
  } finally {
    loading.value = false;
    Taro.hideLoading();
  }
};
// 监听selectedIds变化
watch(
  () => shipmentStore.selectedIds,
  (newIds) => {
    console.log("selectedIds变化了", newIds);
    queryPlanAll(newIds);
  },
  { immediate: true, deep: true }
);

// 监听detailList变化，强制刷新swipe组件
watch(
  () => shipmentStore.detailList,
  (newList, oldList) => {
    console.log("detailList数据变化了", newList);

    // 如果数据发生变化，强制刷新组件
    if (JSON.stringify(newList) !== JSON.stringify(oldList)) {
      forceUpdateKey.value += 1;
      resetSwipeStates();
      console.log("强制刷新swipe组件，key:", forceUpdateKey.value);
    }
  },
  { immediate: false, deep: true }
);
onMounted(() => {
  // 获取路由参数中的发货单号
  const { deliveryOrderNo } = router.params;

  if (deliveryOrderNo) {
    fetchDeliveryOrderDetail(deliveryOrderNo);
  } else {
    Taro.showToast({
      title: "缺少发货单号参数",
      icon: "none",
    });
  }
});
useDidShow(() => {
  console.log("页面didShow");

  // 页面显示时强制刷新swipe组件，确保左滑功能正常
  nextTick(() => {
    forceUpdateKey.value += 1;
    resetSwipeStates();
    console.log("页面显示时强制刷新swipe组件");
  });
});
</script>

<style lang="less">
.shipment-edit {
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 0;
  // 防止闪屏的关键优化
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  will-change: auto;
  // 防止背景重绘
  background-attachment: local;
  .header-info {
    border-radius: 16rpx;
    background: #fff;

    /* 下层投影 */
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
    padding: 16rpx 24rpx 1rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .info-content {
      flex: 1;
    }
    .info-item {
      margin-bottom: 16rpx;

      .label {
        color: rgba(29, 29, 29, 0.6);
        font-style: normal;
        font-weight: 400;
      }
      .label1 {
        color: #2f3133;
        font-size: 32rpx;
        font-style: normal;
        font-weight: 600;
      }

      .value {
        color: #1d1d1d;
        font-style: normal;
        font-weight: 400;
      }
      .value1 {
        flex: 1;
        overflow-x: auto;
        color: #2f3133;
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 50rpx;
        border-bottom: 1px solid #eef1f5;
      }
    }
  }

  .detail-section,
  .shipping-section {
    border-radius: 16rpx;
    background: #fff;

    /* 下层投影 */
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    margin-top: 20rpx;
  }

  .section-title {
    color: var(--, var(--, #1a43ad));
    font-style: normal;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;
    font-size: 32rpx;
    border-bottom: 1px solid #eef1f5;

    .add-btn {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .detail-list {
    .detail-item {
      padding: 24rpx;
      border-bottom: 1px solid #eef1f5;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;

      &.highlight {
        position: relative;
      }
      .item-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16rpx;

        .item-status {
          padding: 4rpx 12rpx;
          border-radius: 20rpx;
          font-size: 20rpx;
          margin-right: 16rpx;

          &.urgent {
            background-color: #faebeb;
            color: #fc474c;
          }
        }

        .plan-number {
          font-size: 28rpx;
          color: #2f3133;
          font-weight: 500;
          flex: 1;
        }

        .refresh-icon {
          padding: 8rpx;
        }
      }

      .item-content {
        flex: 1;
        .content-row {
          display: flex;
          margin-bottom: 12rpx;
          font-size: 26rpx;

          .content-label {
            color: #8d9094;
            min-width: 180rpx;
          }

          .content-value {
            color: #2f3133;
            flex: 1;
          }
        }
      }
    }
  }

  .delete-button {
    width: 64rpx;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fc474c;
    color: #fff;
    font-size: 24rpx;
  }

  .shipping-section {
    // 防止闪屏的关键优化
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    will-change: auto;

    .shipping-info {
      .info-item {
        display: flex;
        align-items: center;
        padding: 24rpx;
        border-bottom: 1px solid #eef1f5;
        // 防止闪屏优化
        transform: translateZ(0);
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;

        &:last-child {
          border-bottom: none;
        }

        .info-label {
          color: #2f3133;
          font-size: 28rpx;
          font-weight: 400;
          min-width: 160rpx;
          margin-right: 20rpx;
        }

        .info-value-container {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .info-value {
          color: #8d9094;
          font-size: 28rpx;
          flex: 1;
          text-align: right;
          margin-right: 16rpx;
        }

        .info-input {
          flex: 1;
          color: #2f3133;
          font-size: 28rpx;
          text-align: right;
          margin-right: 16rpx;
          border: none;
          outline: none;
          background: transparent;

          &::placeholder {
            color: #c8c9cc;
            font-size: 28rpx;
          }
        }

        .switch-container {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          gap: 16rpx;
          color: #8d9094;
          font-size: 28rpx;
          // 防止闪屏优化
          transform: translateZ(0);
          backface-visibility: hidden;
          -webkit-backface-visibility: hidden;
        }

        // 开关项特殊优化
        &.switch-item {
          // 强制硬件加速
          transform: translate3d(0, 0, 0);
          backface-visibility: hidden;
          -webkit-backface-visibility: hidden;
          // 防止重排
          contain: layout style paint;
        }
      }
    }

    .remark-section {
      padding: 24rpx;
      border-bottom: 1px solid #eef1f5;

      .remark-textarea {
        width: 100%;
        min-height: 180rpx;
        padding: 16rpx;
        border-radius: 16rpx;
        background: #f5f7fa;
        border: none;
        outline: none;
        font-size: 28rpx;
        color: #2f3133;
        resize: none;
        box-sizing: border-box;
        // 防止闪屏优化
        transform: translateZ(0);
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        transition: none !important;

        &::placeholder {
          color: #c8c9cc;
          font-size: 28rpx;
        }
      }
    }

    .image-section {
      padding: 24rpx;

      .image-label {
        color: #2f3133;
        font-size: 28rpx;
        font-weight: 400;
        margin-bottom: 16rpx;
      }

      .image-gallery {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;

        .image-item {
          position: relative;
          width: 128rpx;
          height: 128rpx;
          border-radius: 12rpx;
          overflow: hidden;

          .gallery-image {
            width: 100%;
            height: 100%;
            border-radius: 12rpx;
          }

          .delete-icon {
            position: absolute;
            top: -8rpx;
            right: -8rpx;
            width: 32rpx;
            height: 32rpx;
            background-color: #ff4d4f;
            color: #fff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20rpx;
            font-weight: bold;
            z-index: 10;
          }
        }

        .upload-button {
          width: 128rpx;
          height: 128rpx;
          border: 2rpx dashed #ddd;
          border-radius: 12rpx;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background-color: #fafafa;

          &:active {
            background-color: #f0f0f0;
          }

          .upload-text {
            font-size: 20rpx;
            color: #999;
            margin-top: 8rpx;
          }
        }
      }
    }
  }

  .bottom-button {
    padding: 40rpx 80rpx;
    background-color: #fff;
    // padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  }

  // NutUI 组件闪屏优化
  :deep(.nut-switch) {
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    transition: none !important;

    .nut-switch__button {
      transition: none !important;
      transform: translateZ(0);
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
    }
  }

  :deep(.nut-uploader) {
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }

  // NutUI Swipe 组件优化，确保数据更新后左滑功能正常
  :deep(.nut-swipe) {
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    // 确保滑动容器正常工作
    overflow: hidden;

    .nut-swipe__wrapper {
      transform: translateZ(0);
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
      transition: transform 0.3s ease;
    }

    .nut-swipe__left,
    .nut-swipe__right {
      transform: translateZ(0);
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
    }
  }
}
</style>
