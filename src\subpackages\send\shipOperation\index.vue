<template>
  <view class="shipment-page">
    <!-- Tab切换 -->
    <view style="padding: 0 100rpx">
      <view class="tab-container">
        <view
          class="tab-item"
          :class="{ active: activeTab === 0 }"
          @tap="switchTab(0)"
          >待确认发货</view
        >
        <view
          class="tab-item"
          :class="{ active: activeTab === 1 }"
          @tap="switchTab(1)"
          >已确认发货</view
        >
      </view>
    </view>

    <!-- 列表内容 -->
    <!-- <view v-if="shopList.length === 0" class="task-panel">
      <image class="empty" src="/static/image/bgs/empty.png" mode=""></image>
      <view class="empty-text">暂无数据<br />请联系管理员</view>
    </view> -->
    <view class="shipment-list-panel">
      <scroll-view
        scroll-y="true"
        class="shipment-list"
        @scrolltolower="loadMore"
        lower-threshold="100"
        enable-back-to-top="true"
        refresher-enabled="true"
        :refresher-triggered="refreshing"
        @refresherrefresh="onRefresh"
      >
        <view
          class="shipment-card"
          v-for="(item, index) in shipmentList"
          :key="index"
          @tap="goToDetail(item.deliveryOrderNo)"
        >
          <view class="shipment-header">
            <view class="shipment-title">发货单号</view>
            <view class="shipment-number">{{ item.deliveryOrderNo }}</view>
          </view>
          <view class="divider"></view>
          <view class="shipment-info">
            <!-- 兼容原有字段显示 -->
            <view
              class="info-item"
              v-if="item.customerName && !item.materialName"
            >
              <text class="label">客户名称：</text>
              <text class="value">{{ item.customerName }}</text>
            </view>
            <view class="info-item">
              <text class="label">收货地址：</text>
              <text class="value">{{ item.deliveryAddress }}</text>
            </view>
            <view class="info-item">
              <text class="label">品类数：</text>
              <text class="value">{{ item.categoryCount }}</text>
            </view>
            <view class="info-item" v-if="activeTab === 0">
              <text class="label">距最近交货时间：</text>
              <text class="value" :class="getTimeClass(item.maxDeliveryTime)">
                {{ getDaysRemaining(item.maxDeliveryTime) }}
              </text>
            </view>
            <view class="info-item" v-else>
              <text class="label">发货时间：</text>
              <text class="value">
                {{ item.deliveryTime }}
              </text>
            </view>
          </view>
        </view>

        <view class="loading" v-if="loading">加载中...</view>
        <view
          class="no-more"
          v-if="!hasMore && !loading && shipmentList.length > 0"
        >
          没有更多数据了
        </view>
        <view class="empty-state" v-if="!loading && shipmentList.length === 0">
          <image
            class="empty-icon"
            src="../../../images/noData.png"
            mode="aspectFit"
          ></image>
          <view class="empty-text">暂无数据</view>
        </view>
        <!-- 添加底部空间，确保可以滚动到底部 -->
        <view class="bottom-space"></view>
      </scroll-view>
    </view>

    <!-- 扫码按钮 -->
    <view class="scan-button">
      <view class="scan-button-icon" @tap="handleScan">
        <image
          style="width: 200rpx; height: 200rpx"
          src="../../../images/saoma.png"
          mode="aspectFit"
        ></image>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import Taro from "@tarojs/taro";
import { getShipmentList } from "@/api/shipment";

const userInfo = JSON.parse(Taro.getStorageSync("userInfo"));
console.log(userInfo, "userInfo");

// 当前激活的tab
const activeTab = ref(0);
// 列表数据
const shipmentList = ref<any[]>([]);
// 分页参数
const page = ref(1);
const pageSize = ref(10);
const loading = ref(false);
const hasMore = ref(true);
const refreshing = ref(false);

// 切换tab
const switchTab = (index: number) => {
  if (activeTab.value === index) return;
  activeTab.value = index;
  // 重置分页和状态
  resetPagination();
  fetchShipmentList();
};

// 重置分页状态
const resetPagination = () => {
  page.value = 1;
  shipmentList.value = [];
  hasMore.value = true;
};

// 获取时间标签的样式类
const getTimeClass = (dateStr: string) => {
  if (!dateStr) return "normal";

  // 解析传入的日期
  const targetDate = new Date(dateStr);
  if (isNaN(targetDate.getTime())) return "normal"; // 处理无效日期

  // 当前日期（去掉时间部分）
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // 计算天数差异（毫秒转天数）
  const timeDiff = targetDate.getTime() - today.getTime();
  const daysRemaining = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

  // 应用原有逻辑
  if (daysRemaining <= 3) return "urgent";
  if (daysRemaining <= 14) return "warning";
  return "normal";
};
// 获取剩余天数
const getDaysRemaining = (dateStr: string) => {
  if (!dateStr) return "0";

  // 解析传入的日期
  const targetDate = new Date(dateStr);
  if (isNaN(targetDate.getTime())) return "0"; // 处理无效日期

  // 当前日期（去掉时间部分）
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // 计算天数差异（毫秒转天数）
  const timeDiff = targetDate.getTime() - today.getTime();
  const daysRemaining = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

  return daysRemaining > 0 ? `${daysRemaining}天` : "已逾期";
};
// 获取发货单列表
const fetchShipmentList = async () => {
  if (loading.value || !hasMore.value) return;
  loading.value = true;

  try {
    const status = activeTab.value === 0 ? "1" : "2";
    const res = await getShipmentList({
      pageNum: page.value,
      pageSize: pageSize.value,
      status,
      supplierCreditCode: userInfo.entCode,
    });

    console.log("API响应数据:", res);

    // 处理API响应数据
    if (res && res.success && res.data) {
      // 根据实际API响应结构处理数据
      // 如果API返回的是分页数据结构 { list: [], total: number, pageNum: number, size: number }
      if (res.data.list && Array.isArray(res.data.list)) {
        const { list = [], total = 0, pageNum = 1 } = res.data;

        // 更新列表数据
        if (page.value === 1) {
          shipmentList.value = list;
        } else {
          shipmentList.value = [...shipmentList.value, ...list];
        }

        // 判断是否还有更多数据
        const totalLoaded = shipmentList.value.length;
        hasMore.value = totalLoaded < total;

        // 页码递增
        page.value++;

        console.log(
          `加载完成，当前页码：${pageNum}，当前总数：${totalLoaded}/${total}，还有更多：${hasMore.value}`
        );
      }
      // 如果API直接返回数组
      else if (Array.isArray(res.data)) {
        if (page.value === 1) {
          shipmentList.value = res.data;
        } else {
          shipmentList.value = [...shipmentList.value, ...res.data];
        }

        // 如果返回的数据少于请求的pageSize，说明没有更多数据了
        hasMore.value = res.data.length >= pageSize.value;
        page.value++;

        console.log(
          `加载完成，当前页码：${page.value - 1}，本次加载：${
            res.data.length
          }条，还有更多：${hasMore.value}`
        );
      } else {
        // 数据格式不符合预期
        if (page.value === 1) {
          shipmentList.value = [];
        }
        hasMore.value = false;
        console.log("API返回数据格式不符合预期:", res.data);
      }
    } else {
      // API调用失败或无数据
      if (page.value === 1) {
        shipmentList.value = [];
      }
      hasMore.value = false;
      console.log("API调用失败或无数据:", res);

      // 显示API返回的错误信息
      if (res && res.message) {
        Taro.showToast({
          title: res.message,
          icon: "none",
        });
      }
    }
  } catch (error) {
    console.error("获取发货单列表失败", error);
    Taro.showToast({
      title: "获取数据失败",
      icon: "none",
    });
    // 发生错误时，如果是第一页，清空列表
    if (page.value === 1) {
      shipmentList.value = [];
    }
    hasMore.value = false;
  } finally {
    loading.value = false;
  }
};

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loading.value) {
    fetchShipmentList();
  }
};

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true;
  try {
    // 重置分页状态
    page.value = 1;
    hasMore.value = true;

    // 重新获取数据
    await fetchShipmentList();
  } finally {
    refreshing.value = false;
  }
};

// 跳转到发货单详情页面
const goToDetail = (deliveryOrderNo: string | undefined) => {
  if (activeTab.value === 0) {
    Taro.navigateTo({
      url: `/subpackages/send/shipEdit/index?deliveryOrderNo=${deliveryOrderNo}`,
    });
  } else {
    Taro.navigateTo({
      url: `/subpackages/send/shipDetail/index?deliveryOrderNo=${deliveryOrderNo}`,
    });
  }
};

// 扫码功能
const handleScan = () => {
  Taro.scanCode({
    success: (res) => {
      // dlbnb-供应商entcode-客户entcode-88.588.000071/FH00006920250623017
      console.log("扫码结果", res, userInfo.entCode);
      const arr = res.result.split("-");
      if (arr.includes("dlbnb")) {
        if (arr[1] !== userInfo.entCode) {
          return Taro.showToast({
            title: "没有权限访问该发货单",
            icon: "none",
          });
        }
        Taro.navigateTo({
          url: `/subpackages/send/shipEdit/index?deliveryOrderNo=${
            arr[arr.length - 1]
          }`,
        });
        Taro.showToast({
          title: "扫码成功",
          icon: "none",
          duration: 1000,
        });
      } else {
        Taro.showToast({
          title: "二维码不正确",
          icon: "none",
        });
      }
    },
    fail: (err) => {
      console.error("扫码失败", err);
      // Taro.showToast({
      //   title: "扫码失败",
      //   icon: "none",
      // });
    },
  });
};

onMounted(() => {
  fetchShipmentList();
});
</script>

<style lang="less">
.shipment-page {
  height: 100vh; // 用 height 而不是 min-height
  display: flex;
  flex-direction: column;
  padding: 0 30rpx 30rpx 30rpx;
  box-sizing: border-box;
}
.shipment-list-panel {
  margin-top: 30rpx;
  flex: 1;
  overflow: hidden; // 防止外层滚动
}

.shipment-list {
  height: 100%;
}
.tab-container {
  height: 68rpx;
  border-radius: 34rpx;
  background: #fff;

  /* 下层投影 */
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-sizing: border-box;
  margin-top: 20rpx;

  .tab-item {
    flex: 1;
    height: 44rpx;
    line-height: 44rpx;
    text-align: center;
    font-size: 28rpx;
    color: #8d9094;
    position: relative;

    &.active {
      color: #303030;
      font-weight: 500;

      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 50%;
        transform: translateX(-50%);
        height: 2px;
        background-color: #4068f5;
      }
    }
  }
}

.shipment-card {
  border-radius: 16rpx;
  background: #fff;

  /* 下层投影 */
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
  padding: 20rpx;
  margin-bottom: 20rpx;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .shipment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .shipment-title {
    font-size: 28rpx;
    color: #1a43ad;
    font-weight: 600;
  }

  .shipment-number {
    font-size: 28rpx;
    font-weight: 400;
    color: #000;
  }

  .divider {
    height: 1px;
    background-color: #ced4db;
    margin: 20rpx 0;
  }

  .shipment-info {
    .info-item {
      margin-bottom: 12px;

      .label {
        color: #8d9094;
      }

      .value {
        color: #2f3133;
      }

      .urgent {
        color: #fc474c;
        background-color: #faebeb;
        padding: 4px 12px;
        border-radius: 20px;
      }

      .warning {
        color: #fa9600;
        background-color: #faf2e6;
        padding: 4px 12px;
        border-radius: 20px;
      }

      .normal {
        color: #0d6ce4;
        background-color: #e6effa;
        padding: 4px 12px;
        border-radius: 20px;
      }
    }
  }
}

.loading,
.no-more {
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 24rpx;
}

.bottom-space {
  height: 250rpx; /* 为底部扫码按钮预留空间，确保可以滚动到底部 */
}

.debug-button {
  background: #4068f5;
  color: white;
  text-align: center;
  padding: 20rpx;
  margin: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.scan-button {
  width: 100vw;
  height: 300rpx;
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  background: url("../../../images/Union.png") no-repeat;
  background-size: cover;
  .scan-button-icon {
    position: absolute;
    top: 38%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
