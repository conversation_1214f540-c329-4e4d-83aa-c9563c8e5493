{"name": "taro-vue", "version": "1.0.0", "private": true, "description": "基于 Taro4 + Vue3 + TypeScript + Webpack5 + Less + NutUI 开发的模板项目", "templateInfo": {"name": "default", "typescript": true, "css": "Less", "framework": "Vue3"}, "scripts": {"build:weapp": "taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "build:harmony-hybrid": "taro build --type harmony-hybrid", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "dev:harmony-hybrid": "npm run build:harmony-hybrid -- --watch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "theMuseCatcher", "license": "MIT", "dependencies": {"@babel/runtime": "^7.26.0", "@nutui/icons-vue-taro": "^0.0.9", "@nutui/nutui-taro": "^4.3.13", "@tarojs/components": "4.0.9", "@tarojs/helper": "4.0.9", "@tarojs/plugin-framework-vue3": "4.0.9", "@tarojs/plugin-html": "4.0.9", "@tarojs/plugin-platform-alipay": "4.0.9", "@tarojs/plugin-platform-h5": "4.0.9", "@tarojs/plugin-platform-harmony-hybrid": "^4.0.9", "@tarojs/plugin-platform-jd": "4.0.9", "@tarojs/plugin-platform-qq": "4.0.9", "@tarojs/plugin-platform-swan": "4.0.9", "@tarojs/plugin-platform-tt": "4.0.9", "@tarojs/plugin-platform-weapp": "4.0.9", "@tarojs/runtime": "4.0.9", "@tarojs/shared": "4.0.9", "@tarojs/taro": "4.0.9", "md5": "^2.3.0", "pinia": "^2.3.0", "vue": "^3.5.13"}, "devDependencies": {"@babel/core": "^7.26.0", "@nutui/auto-import-resolver": "^1.0.0", "@tarojs/cli": "4.0.9", "@tarojs/taro-loader": "4.0.9", "@tarojs/test-utils-vue3": "^0.1.1", "@tarojs/webpack5-runner": "4.0.9", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/webpack-env": "^1.18.5", "@typescript-eslint/eslint-plugin": "^8.20.0", "@typescript-eslint/parser": "^8.20.0", "@vue/babel-plugin-jsx": "^1.2.5", "@vue/compiler-sfc": "^3.5.13", "babel-preset-taro": "4.0.9", "css-loader": "^7.1.2", "eslint": "^9.18.0", "eslint-config-taro": "4.0.9", "eslint-plugin-vue": "^9.32.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.1", "style-loader": "^4.0.0", "stylelint": "^16.13.2", "ts-node": "^10.9.2", "tsconfig-paths-webpack-plugin": "^4.2.0", "typescript": "^5.7.3", "unplugin-vue-components": "^0.25.2", "vue-loader": "^17.4.2", "webpack": "^5.97.1"}}