# taro-vue

*本跨端模板项目基于 `@tarojs/taro@4.0.8` + `vue@^3.5.13`+ `typescript@^5.7.3` + `webpack@^5.97.1` + `@nutui/nutui-taro@^4.3.13` + `@nutui/icons-vue-taro@^0.0.9` 实现！*

## References

- [Taro 文档](https://docs.taro.zone/docs/)
- [NutUI 文档](https://nutui.jd.com/taro/vue/4x/#/zh-CN/guide/intro)
- [NutUI Icons](https://nutui.jd.com/taro/vue/4x/#/zh-CN/component/icon)

## Project

```sh
<NAME_EMAIL>:themusecatcher/taro-vue.git
```

## Project setup

```sh
cd taro-vue
pnpm install
```

## Compiles and hot-reloads for development

*以小程序为例*

```sh
cd taro-vue
pnpm dev:weapp
```

## Compiles and minifies for production

*以小程序为例*

```sh
pnpm build:weapp
```
