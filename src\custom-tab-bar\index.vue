<script lang="ts">
export default {
  options: {
    addGlobalClass: true, // 解决tabbar样式隔离问题
  },
};
</script>
<script setup lang="ts">
import { reactive } from "vue";
import Taro from "@tarojs/taro";
import { storeToRefs } from "pinia";
import { useSelectedStore } from "@/store/modules/selected";

const store = useSelectedStore();
const { selected } = storeToRefs(store);

const deviceInfo = Taro.getDeviceInfo(); // 获取设备基础信息
const windowInfo = Taro.getWindowInfo(); // 获取窗口信息
const appBaseInfo = Taro.getAppBaseInfo(); // 获取微信APP基础信息
const theme: "light" | "dark" = appBaseInfo.theme || "light";
console.log("deviceInfo", deviceInfo);
console.log("windowInfo", windowInfo);
console.log("appBaseInfo", appBaseInfo);
console.log("theme", theme);

const themeStyle = {
  light: {
    color: "#B7B7B7",
    activeColor: " #1A43AD",
    backgroundColor: "#ffffff",
  },
  dark: {
    color: "#B7B7B7",
    activeColor: " #1A43AD",
    backgroundColor: "#ffffff",
  },
};
interface TabBar {
  title: string;
  iconActive: string;
  iconInactive: string;
  url: string;
}
const tabBarList = reactive<TabBar[]>([
  {
    title: "首页",
    iconActive: "/images/home_active.png",
    iconInactive: "/images/home_unactive.png",
    url: "/pages/index/index",
  },
  {
    title: "待办",
    iconActive: "/images/find_active.png",
    iconInactive: "/images/find_unactive.png",
    url: "/pages/find/index",
  },
  {
    title: "我的",
    iconActive: "/images/my_active.png",
    iconInactive: "/images/my_unactive.png",
    url: "/pages/my/index",
  },
]);
function switchTab(index: number, url: string) {
  const isAuthorized = Taro.getStorageSync("token") || false;
  const authorizeInterception = ["/pages/my/index"];
  if (!isAuthorized && authorizeInterception.includes(url)) {
    Taro.navigateTo({
      url: `/subpackages/login/index?redirect=${encodeURIComponent(
        url
      )}&index=${index}`,
    });
  } else {
    store.setSelected(index);
    Taro.switchTab({ url });
  }
}
</script>
<template>
  <view
    class="m-tab-bar"
    :style="`background-color: ${themeStyle[theme].backgroundColor};`"
  >
    <view
      class="m-tab-bar-item"
      v-for="(tabBar, index) in tabBarList"
      :key="index"
      @tap="switchTab(index, tabBar.url)"
    >
      <image
        class="u-icon"
        :src="selected === index ? tabBar.iconActive : tabBar.iconInactive"
        mode="aspectFit"
      />
      <text
        class="u-view"
        :style="{
          color:
            selected === index
              ? themeStyle[theme].activeColor
              : themeStyle[theme].color,
        }"
        >{{ tabBar.title }}</text
      >
    </view>
  </view>
</template>
<style lang="less">
.m-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: calc(100px + env(safe-area-inset-bottom));
  box-sizing: border-box;
  background: #fff;
  padding-top: 26rpx;

  /* 下层投影 */
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
  display: flex;
  z-index: 999;
  .m-tab-bar-item {
    flex: 1;
    text-align: center;
    display: flex;
    align-items: center;
    flex-direction: column;
    .u-icon {
      font-size: 48rpx;
      width: 48rpx;
      height: 48rpx;
    }
    .u-view {
      font-weight: 400;
      font-size: 24px;
      line-height: 32px;
    }
  }
}
</style>
