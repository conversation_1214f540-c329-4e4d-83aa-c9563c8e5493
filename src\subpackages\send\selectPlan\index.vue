<template>
  <view class="select-plan-page">
    <!-- 客户信息头部 -->
    <view class="customer-header">
      <view class="info-content">
        <view class="customer-info">
          <view class="label">客户名称</view>
          <view class="value customer-name">{{
            customerInfo.customerName
          }}</view>
        </view>
        <view class="customer-info">
          <view class="label">企业前缀</view>
          <view class="value">{{ customerInfo.enterpriseCode }}</view>
        </view>
      </view>
      <view class="header-image">
        <image
          style="width: 120rpx; height: 120rpx"
          src="../../../images/plan.png"
          mode="aspectFit"
        />
      </view>
    </view>

    <!-- 供货计划列表 -->
    <view class="plan-bottom-list">
      <scroll-view
        scroll-y="true"
        class="plan-list"
        lower-threshold="50"
        enable-back-to-top="true"
        @scrolltolower="loadMore"
      >
        <view
          v-for="plan in planList"
          :key="plan.id"
          class="plan-item"
          :class="{ selected: selectedPlans.includes(plan.id) }"
        >
          <!-- 使用NutUI的checkbox -->
          <view class="checkbox-wrapper">
            <nut-checkbox
              :model-value="selectedPlans.includes(plan.id)"
              @update:model-value="
                (checked) => togglePlanSelection(plan, checked)
              "
              icon-size="20"
            />
          </view>

          <!-- 计划信息 -->
          <view class="plan-content">
            <!-- 计划编号和状态标签 -->
            <view class="plan-header">
              <text class="plan-no">{{ plan.planCode }}</text>
              <view
                class="status-tag"
                :class="getTimeClass(plan.planDeliveryTime)"
              >
                <text>{{ getDaysRemaining(plan.planDeliveryTime) }}</text>
              </view>
            </view>

            <!-- 物料信息 -->
            <view class="material-info">
              <text class="material-name"
                >物料名称（客）：{{ plan.materialName }}</text
              >
            </view>

            <!-- 计划数量 -->
            <view class="plan-quantity">
              <text>计划数量：{{ plan.planQuantity }}{{ plan.unitName }}</text>
            </view>

            <!-- 计划发货时间 -->
            <view class="plan-date">
              <text>计划发货时间：{{ plan.planDeliveryTime }}</text>
            </view>

            <!-- 计划备注 -->
            <view class="plan-remarks" v-if="plan.remarkSwitch">
              <text>计划备注：{{ plan.remark || "" }}</text>
            </view>
          </view>
        </view>

        <!-- 加载更多提示 -->
        <view v-if="loading" class="loading-more">
          <view class="loading-spinner">⏳</view>
          <text>加载中...</text>
        </view>

        <view v-if="!hasMore && planList.length > 0" class="no-more">
          <text>没有更多数据了</text>
        </view>
        <view class="empty-state" v-if="!loading && planList.length === 0">
          <image
            class="empty-icon"
            src="../../../images/noData.png"
            mode="aspectFit"
          ></image>
          <view class="empty-text">暂无数据</view>
        </view>
        <view class="list-space"></view>
      </scroll-view>
    </view>

    <!-- 底部操作区 -->
    <view class="bottom-actions">
      <view class="select-all-section">
        <nut-checkbox
          :model-value="isAllSelected"
          @update:model-value="toggleSelectAll"
          icon-size="20"
        >
          全选
        </nut-checkbox>
      </view>

      <view class="confirm-section">
        <nut-button
          type="primary"
          class="confirm-btn"
          @click="confirmSelection"
        >
          确认添加{{
            selectedPlans.length > 0 ? `(${selectedPlans.length})` : ""
          }}
        </nut-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import Taro, { useRouter } from "@tarojs/taro";
import type { SupplyPlan, CustomerInfo } from "@/types/plan";
import { queryCooperationLList } from "@/api/shipment";
import { useShipmentStore } from "@/store/modules/shipment";

const shipmentStore = useShipmentStore();

// 路由参数
const router = useRouter();
const params = router.params as any;

// 响应式数据
const customerInfo = ref<CustomerInfo>({
  customerName: "",
  enterpriseCode: "",
  customerEntCode: "",
});

const planList = ref<SupplyPlan[]>([]);
const selectedPlans = ref<string[]>([]);
const loading = ref(false);
const hasMore = ref(true);
// 分页参数
const page = ref(1);
const pageSize = ref(10);
const selectedPlanAll: any = ref([]);
// 计算属性
const isAllSelected = computed(() => {
  return (
    planList.value.length > 0 &&
    selectedPlans.value.length === planList.value.length
  );
});

// 获取时间标签的样式类
const getTimeClass = (dateStr: string) => {
  if (!dateStr) return "normal";

  // 解析传入的日期
  const targetDate = new Date(dateStr);
  if (isNaN(targetDate.getTime())) return "normal"; // 处理无效日期

  // 当前日期（去掉时间部分）
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // 计算天数差异（毫秒转天数）
  const timeDiff = targetDate.getTime() - today.getTime();
  const daysRemaining = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

  // 应用原有逻辑
  if (daysRemaining <= 3) return "urgent";
  if (daysRemaining <= 14) return "warning";
  return "normal";
};
// 获取剩余天数
const getDaysRemaining = (dateStr: string) => {
  if (!dateStr) return "0";

  // 解析传入的日期
  const targetDate = new Date(dateStr);
  if (isNaN(targetDate.getTime())) return "0"; // 处理无效日期

  // 当前日期（去掉时间部分）
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // 计算天数差异（毫秒转天数）
  const timeDiff = targetDate.getTime() - today.getTime();
  const daysRemaining = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

  return daysRemaining > 0 ? `距离交货${daysRemaining}天` : "已逾期";
};
// 方法
const loadPlanData = async () => {
  if (loading.value || !hasMore.value) return;

  loading.value = true;
  try {
    const res = await queryCooperationLList({
      status: 1,
      filterPlanCodeList: shipmentStore.selectedPlanNos,
      deliverOrderNull: 2,
      source: 2,
      customerEntCode: customerInfo.value.customerEntCode,
      pageNum: page.value,
      pageSize: pageSize.value,
    });

    console.log("API响应数据:", res);

    // 处理API响应数据
    if (res && res.success && res.data) {
      // 根据实际API响应结构处理数据
      // 如果API返回的是分页数据结构 { list: [], total: number, pageNum: number, size: number }
      if (res.data.list && Array.isArray(res.data.list)) {
        const { list = [], total = 0, pageNum = 1 } = res.data;

        // 更新列表数据
        if (page.value === 1) {
          planList.value = list;
        } else {
          planList.value = [...planList.value, ...list];
        }

        // 判断是否还有更多数据
        const totalLoaded = planList.value.length;
        hasMore.value = totalLoaded < total;

        // 页码递增，为下次加载做准备
        if (hasMore.value) {
          page.value++;
        }

        console.log(
          `加载完成，当前页码：${pageNum}，当前总数：${totalLoaded}/${total}，还有更多：${hasMore.value}`
        );
      }
      // 如果API直接返回数组
      else if (Array.isArray(res.data)) {
        if (page.value === 1) {
          planList.value = res.data;
        } else {
          planList.value = [...planList.value, ...res.data];
        }

        // 如果返回的数据少于请求的pageSize，说明没有更多数据了
        hasMore.value = res.data.length >= pageSize.value;

        // 页码递增，为下次加载做准备
        if (hasMore.value) {
          page.value++;
        }

        console.log(
          `加载完成，当前页码：${
            page.value - (hasMore.value ? 1 : 0)
          }，本次加载：${res.data.length}条，还有更多：${hasMore.value}`
        );
      } else {
        // 数据格式不符合预期
        if (page.value === 1) {
          planList.value = [];
        }
        hasMore.value = false;
        console.log("API返回数据格式不符合预期:", res.data);
      }
    } else {
      // API调用失败或无数据
      if (page.value === 1) {
        planList.value = [];
      }
      hasMore.value = false;
      console.log("API调用失败或无数据:", res);

      // 显示API返回的错误信息
      if (res && res.message) {
        Taro.showToast({
          title: res.message,
          icon: "none",
        });
      }
    }
  } catch (error) {
    console.error("获取发货单列表失败", error);
    Taro.showToast({
      title: "获取数据失败",
      icon: "none",
    });
    // 发生错误时，如果是第一页，清空列表
    if (page.value === 1) {
      planList.value = [];
    }
    hasMore.value = false;
  } finally {
    loading.value = false;
  }
};

const togglePlanSelection = (plan: any, checked?: boolean) => {
  const index = selectedPlans.value.indexOf(plan.id);

  if (checked !== undefined) {
    // 来自checkbox的事件
    if (checked && index === -1) {
      selectedPlans.value.push(plan.id);
      selectedPlanAll.value.push(plan);
    } else if (!checked && index > -1) {
      selectedPlans.value.splice(index, 1);
      selectedPlanAll.value.splice(index, 1);
    }
  } else {
    // 来自点击事件
    if (index > -1) {
      selectedPlans.value.splice(index, 1);
      selectedPlanAll.value.splice(index, 1);
    } else {
      selectedPlans.value.push(plan.id);
      selectedPlanAll.value.push(plan);
    }
  }
};

const toggleSelectAll = (checked: boolean) => {
  if (checked) {
    selectedPlans.value = planList.value.map((plan) => plan.id);
    selectedPlanAll.value = planList.value;
  } else {
    selectedPlans.value = [];
    selectedPlanAll.value = [];
  }
};

const loadMore = () => {
  console.log("================================loadMore 触发");
  console.log(
    "hasMore:",
    hasMore.value,
    "loading:",
    loading.value,
    "page:",
    page.value,
    "planList.length:",
    planList.value.length
  );

  // 显示触发提示
  // Taro.showToast({
  //   title: "加载更多触发",
  //   icon: "none",
  //   duration: 1000,
  // });

  if (hasMore.value && !loading.value) {
    loadPlanData();
  }
};

const confirmSelection = () => {
  console.log(shipmentStore.detailList, "====================================");

  if (selectedPlans.value.length === 0) {
    Taro.showToast({
      title: "请选择至少一个供货计划",
      icon: "none",
    });
    return;
  }

  Taro.showToast({
    title: `已选择${selectedPlans.value.length}个计划`,
    icon: "none",
  });
  selectedPlanAll.value.forEach((item) => {
    shipmentStore.selectedIds.push(item.id);
    shipmentStore.selectedPlanNos.push(item.planCode);
    shipmentStore.addDetailItem({
      id: item.id, // 分配唯一ID
      planNo: item.planCode,
      planDeliveryTime: item.planDeliveryTime,
      materialName: item.materialName,
      materialCode: item.materialCode,
      planQuantity: item.planQuantity,
      unit: item.unit,
      unitName: item.unitName,
      // actualQuantity: "", // 初始化实发数量为空字符串，确保字段存在且可编辑
      productName: item.materialName,
      deliveryOrderHerbInfoVOList: [],
    });
  });
  // 返回上一页并传递选中的数据
  setTimeout(() => {
    Taro.navigateBack({
      delta: 1,
    });
  }, 400);
};

// 生命周期
onMounted(() => {
  // 从路由参数获取客户信息
  if (params.customerName) {
    customerInfo.value.customerName = decodeURIComponent(params.customerName);
  }
  if (params.enterpriseCode) {
    customerInfo.value.enterpriseCode = decodeURIComponent(
      params.enterpriseCode
    );
  }
  if (params.customerEntCode) {
    customerInfo.value.customerEntCode = decodeURIComponent(
      params.customerEntCode
    );
  }

  loadPlanData();
});
</script>

<style lang="scss">
.select-plan-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  .list-space {
    // height: 30rpx; // 底部占位空间，确保最后一个元素可以完全显示
  }

  .plan-bottom-list {
    flex: 1;
    height: 0; // 关键：让 flex 子元素正确计算高度

    .plan-list {
      height: 100%;
    }
  }

  .customer-header {
    border-radius: 16rpx;
    background: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
    padding: 16rpx 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .info-content {
      flex: 1;
    }

    .customer-info,
    .enterprise-info {
      margin-bottom: 16rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        color: #2f3133;
        font-size: 28rpx;
        font-style: normal;
        font-weight: 600;
        margin-right: 8rpx;
      }

      .value {
        color: #2f3133;
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
      }
    }

    .customer-info {
      .label {
        color: #2f3133;
        font-size: 32rpx;
        font-weight: 600;
      }

      .value {
        color: #2f3133;
        font-weight: 400;
      }
      .customer-name {
        line-height: 50rpx;
        border-bottom: 1px solid #eef1f5;
      }
    }

    .header-image {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .plan-item {
    background: white;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    padding: 24rpx;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
    display: flex;
    align-items: center;
    transition: all 0.3s ease;

    // &.selected {
    //   border: 2rpx solid #0478ff;
    //   box-shadow: 0px 2px 12px 0px rgba(4, 120, 255, 0.2);
    // }

    .checkbox-wrapper {
      margin-right: 20rpx;
      margin-top: 12rpx;
    }

    .plan-content {
      flex: 1;

      .plan-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20rpx;

        .plan-no {
          font-size: 32rpx;
          font-weight: 600;
          color: #2f3133;
          flex: 1;
          margin-right: 16rpx;
        }

        .status-tag {
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
          font-size: 24rpx;
          white-space: nowrap;
          flex-shrink: 0;

          &.urgent {
            color: #fc474c;
            background: #faebeb;
          }

          &.warning {
            color: #fa9600;
            background: #faf2e6;
          }

          &.normal {
            color: #0d6ce4;
            background: #e6effa;
          }
        }
      }

      .material-info,
      .plan-quantity,
      .plan-date,
      .plan-remarks {
        margin-bottom: 12rpx;

        &:last-child {
          margin-bottom: 0;
        }

        text {
          font-size: 26rpx;
          color: #8d9094;
          line-height: 1.4;
        }

        .material-name {
          color: #2f3133;
          font-weight: 500;
        }
      }
    }
  }

  .loading-more,
  .no-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 32rpx;
    color: #999;
    font-size: 28rpx;

    .loading-spinner {
      margin-right: 16rpx;
      font-size: 32rpx;
      animation: spin 1s linear infinite;
    }

    text {
      margin-left: 16rpx;
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .bottom-actions {
    background: white;
    padding: 20rpx 32rpx;
    margin-bottom: 0;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .select-all-section {
      display: flex;
      align-items: center;
    }

    .confirm-section {
      .confirm-btn {
        min-width: 200rpx;
        height: 80rpx;
        border-radius: 40rpx;
        font-size: 32rpx;
        font-weight: 500;
      }
    }
  }
}
</style>
