{"miniprogramRoot": "dist/", "projectname": "code_cloud_46_wxapp", "description": "taro@3.x+vue@3.x+ts+less开发", "appid": "wx133a00c21fbe5132", "setting": {"urlCheck": true, "es6": true, "enhance": true, "compileHotReLoad": false, "postcss": true, "minified": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false}, "compileType": "miniprogram", "libVersion": "3.4.1", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 4}}