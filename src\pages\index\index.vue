<template>
  <view class="index-page">
    <!-- 自定义头部 -->
    <view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="header-left">
          <!-- 返回按钮 -->
          <view class="back-btn"> </view>
        </view>
        <view class="header-center">
          <text key="2" class="header-title">首页</text>
        </view>
        <view class="header-right"></view>
      </view>
    </view>
    <nut-row :gutter="10">
      <nut-col v-if="names.includes('发货管理')" :span="12" @click="toUrl('1')">
        <image class="image" src="../../images/fahuo.png"></image>
      </nut-col>
      <nut-col v-if="names.includes('收货管理')" :span="12" @click="toUrl('2')">
        <image class="image" src="../../images/shouhuo.png"></image>
      </nut-col>
    </nut-row>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Taro, { useDidShow } from "@tarojs/taro";
import { useSelectedStore } from "@/store/modules/selected";
import { useUserStore } from "@/store/modules/user";

// 设置当前选中的 tab
const names = ref([]);
const selectedStore = useSelectedStore();
const userStore = useUserStore();

// 状态栏高度
const statusBarHeight = ref(44);
// 获取系统信息
useDidShow(() => {
  selectedStore.setSelected(0);
  userStore.initFromStorage();
  names.value = userStore.menuList[0].children
    .filter((item) => item.title === "小程序管理")[0]
    .children.map((item) => item.title);
  console.log("names:", names);
  try {
    Taro.getSystemInfo({
      success: (res) => {
        statusBarHeight.value = res.statusBarHeight || 44;
        console.log("系统信息:", res);
      },
      fail: (err) => {
        console.warn("获取系统信息失败:", err);
        statusBarHeight.value = 44;
      },
    });
  } catch (error) {
    console.warn("获取系统信息异常:", error);
    statusBarHeight.value = 44;
  }
});
const toUrl = (type: string) => {
  if (type === "1") {
    Taro.navigateTo({
      url: `/subpackages/send/shipOperation/index`,
    });
  } else {
    Taro.navigateTo({
      url: `/subpackages/receiving/receivingOperation/index`,
    });
  }
};
</script>

<style lang="less">
.index-page {
  height: 100vh;
  padding: 12vh 40rpx 40rpx 40rpx;
  box-sizing: border-box;
  .image {
    width: 100%;
    height: 190px;
  }
}
</style>
