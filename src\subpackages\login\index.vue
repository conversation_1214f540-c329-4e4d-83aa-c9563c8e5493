<template>
  <view class="login-page">
    <!-- 状态栏占位 -->
    <!-- <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view> -->
    <!-- Logo和欢迎文字 -->
    <view class="header-section">
      <image class="logo" src="../../images/icon_logo.png" mode="aspectFit" />
      <view class="welcome-text">欢迎登陆</view>
      <view class="subtitle">中药行业工业互联网标识应用平台</view>
    </view>

    <!-- 登录表单卡片 -->
    <view class="login-card">
      <!-- 用户名输入框 -->
      <view class="input-group">
        <image
          class="input-icon"
          src="../../images/user.png"
          mode="aspectFit"
        />
        <input
          class="input-field"
          v-model="username"
          placeholder="请输入登录账号"
          type="text"
        />
      </view>

      <!-- 密码输入框 -->
      <view class="input-group">
        <image
          class="input-icon"
          src="../../images/password.png"
          mode="aspectFit"
        />
        <input
          v-if="!showPassword"
          class="input-field"
          v-model="password"
          placeholder="请输入密码"
          type="password"
        />
        <input
          v-if="showPassword"
          class="input-field"
          v-model="password"
          placeholder="请输入密码"
          type="text"
        />
        <view @click="togglePassword">
          <image
            class="eye-icon"
            :src="
              require(showPassword
                ? '../../images/eye-o.png'
                : '../../images/eye-c.png')
            "
            mode="aspectFit"
          />
        </view>
      </view>

      <!-- 协议勾选 -->
      <view class="protocol-section">
        <nut-checkbox v-model="agree" icon-size="16"></nut-checkbox>
        我已阅读并同意
        <text class="link">《用户协议》</text>
        和
        <text class="link">《隐私政策》</text>
      </view>

      <!-- 登录按钮 -->
      <view style="width: 100%">
        <view
          class="login-btn"
          :class="{ loading: loading }"
          @click="handleLogin"
        >
          <text class="btn-text">{{ loading ? "登录中..." : "登录" }}</text>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useUserStore } from "@/store/modules/user";
import Taro, { useLoad } from "@tarojs/taro";
import md5 from "md5";

const userStore = useUserStore();

// 响应式数据
const username = ref("");
const password = ref("");
const loading = ref(false);
const agree = ref(false);
const showPassword = ref(false);
const statusBarHeight = ref(44);
const redirectUrl = ref(""); // 用于存储登录后要跳转的页面

// 获取页面参数
useLoad((options) => {
  console.log("Login page options:", options);
  if (options.redirect) {
    redirectUrl.value = decodeURIComponent(options.redirect);
  }
});

// 检查登录状态
const checkLoginStatus = () => {
  const token = Taro.getStorageSync("token");
  if (token) {
    // 已经有 token，直接跳转到首页
    Taro.switchTab({
      url: "/pages/index/index",
    });
    return true;
  }
  return false;
};

// 获取系统信息
onMounted(() => {
  // 首先检查登录状态
  if (checkLoginStatus()) {
    return; // 如果已登录，直接返回，不需要初始化登录页面
  }

  try {
    Taro.getSystemInfo({
      success: (res) => {
        statusBarHeight.value = res.statusBarHeight || 44;
      },
      fail: () => {
        statusBarHeight.value = 44;
      },
    });
  } catch (error) {
    statusBarHeight.value = 44;
  }
});

// 切换密码显示/隐藏
const togglePassword = () => {
  showPassword.value = !showPassword.value;
};

// 登录处理
const handleLogin = async () => {
  if (!username.value) {
    Taro.showToast({
      title: "请输入账号",
      icon: "none",
      duration: 2000,
    });
    return;
  }
  if (!password.value) {
    Taro.showToast({
      title: "请输入密码",
      icon: "none",
      duration: 2000,
    });
    return;
  }
  if (!agree.value) {
    Taro.showToast({
      title: "请先阅读并同意相关协议",
      icon: "none",
      duration: 2000,
    });
    return;
  }
  loading.value = true;
  try {
    const encryptedPassword = md5(password.value);
    const success = await userStore.login({
      username: username.value,
      password: encryptedPassword,
    });
    if (success) {
      Taro.showToast({
        title: "登录成功",
        icon: "none",
        duration: 500,
      });

      // 登录成功后跳转
      setTimeout(() => {
        if (redirectUrl.value) {
          // 如果有重定向地址，跳转到指定页面
          if (redirectUrl.value.includes("/pages/")) {
            // 如果是 tabBar 页面，使用 switchTab
            Taro.switchTab({ url: redirectUrl.value });
          } else {
            // 其他页面使用 redirectTo
            Taro.redirectTo({ url: redirectUrl.value });
          }
        } else {
          // 默认跳转到首页
          Taro.switchTab({ url: "/pages/index/index" });
        }
      }, 300);
    }
  } catch (error) {
    Taro.showToast({
      title: "登录失败，请稍后重试",
      icon: "none",
      duration: 2000,
    });
  } finally {
    loading.value = false;
  }
};
</script>
<style lang="less">
.login-page {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
  // 状态栏占位
  .status-bar {
    width: 100%;
  }

  // 头部区域
  .header-section {
    position: absolute;
    top: 0;
    z-index: 2;
    height: 600rpx;
    width: 100%;
    text-align: left;
    box-sizing: border-box;
    padding-top: 120rpx;
    padding-left: 40rpx;
    background: url("../../images/login-bg.png") no-repeat 100% 100%;
    background-size: cover;
    .logo {
      width: 80rpx;
      height: 90rpx;
      margin-bottom: 30rpx;
      display: block;
    }

    .welcome-text {
      color: #fff;
      font-size: 40rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 56rpx; /* 140% */
      margin-bottom: 10rpx;
    }

    .subtitle {
      color: #fff;
      font-size: 40rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 56rpx; /* 140% */
    }
  }

  // 登录卡片
  .login-card {
    height: 72vh;
    width: 100%;
    box-sizing: border-box;
    background: #ffffff;
    border-radius: 48rpx 48rpx 0px 0px;
    position: absolute;
    z-index: 3;
    bottom: 0;
    padding: 60rpx 40rpx 0 40rpx;

    // 输入框组
    .input-group {
      position: relative;
      margin-bottom: 56rpx;
      padding: 40rpx 0;
      border-bottom: 3rpx solid #f0f0f0;
      display: flex;
      align-items: center;
      transition: all 0.3s ease;

      &:focus-within {
        border-bottom-color: #1890ff;
        transform: translateY(-2rpx);

        .input-icon {
          opacity: 1;
          transform: scale(1.1);
        }
      }

      &:last-of-type {
        margin-bottom: 40rpx;
      }

      .input-icon {
        width: 52rpx;
        height: 52rpx;
        margin-right: 32rpx;
        opacity: 0.6;
        flex-shrink: 0;
        transition: all 0.3s ease;
      }

      .input-field {
        flex: 1;
        font-size: 34rpx;
        color: #333333;
        border: none;
        outline: none;
        background: transparent;
        height: 52rpx;
        line-height: 52rpx;
        font-weight: 400;

        &::placeholder {
          color: #999999;
          font-size: 32rpx;
          font-weight: 300;
        }

        &:focus {
          color: #1890ff;
        }
      }

      .eye-icon {
        width: 52rpx;
        height: 52rpx;
        opacity: 0.6;
        flex-shrink: 0;
        cursor: pointer;
        transition: all 0.3s ease;
        padding: 8rpx;
        margin: -8rpx;
        border-radius: 50%;
        z-index: 10;

        &:hover {
          opacity: 0.8;
          background: rgba(24, 144, 255, 0.1);
        }

        &:active {
          opacity: 1;
          transform: scale(0.9);
          background: rgba(24, 144, 255, 0.2);
        }
      }
    }

    // 协议区域
    .protocol-section {
      margin: 46rpx 0 200rpx 0;
      font-size: 24rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 40rpx; /* 166.667% */
      color: rgba(29, 29, 29, 0.6);
      display: flex;
      align-items: center;
      .link {
        color: #4684fe;
      }
    }

    // 登录按钮
    .login-btn {
      width: 100%;
      height: 84rpx;
      background: linear-gradient(180deg, #62f8ff 0%, #091899 100%),
        linear-gradient(180deg, #ffbf1a -33.33%, #e55f14 131.94%);
      border-radius: 42rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 1px 1px 6.1px 0px rgba(243, 198, 106, 0.3);

      &.loading {
        opacity: 0.8;
        pointer-events: none;

        .btn-text::after {
          content: "...";
          animation: loading 1.5s infinite;
        }
      }

      .btn-text {
        color: #ffffff;
        font-size: 28rpx;
        font-weight: 600;
        letter-spacing: 2rpx;
      }

      &:active {
        transform: scale(0.98);
        box-shadow: 0 6rpx 16rpx rgba(24, 144, 255, 0.4);
      }
    }
  }
}

@keyframes loading {
  0%,
  20% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>
