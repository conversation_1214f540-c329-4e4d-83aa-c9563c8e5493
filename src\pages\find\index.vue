<template>
  <view class="find-page">
    <!-- 自定义头部 -->
    <view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="header-left">
          <!-- 返回按钮 -->
          <view class="back-btn" @click="goBack">
            <text key="1" class="back-icon"
              ><IconFont name="left" size="16" color="#fff"
            /></text>
          </view>
        </view>
        <view class="header-center">
          <text key="2" class="header-title">待办</text>
        </view>
        <view class="header-right"></view>
      </view>
    </view>

    <!-- 空状态内容 -->
    <view class="empty-state">
      <view class="empty-content">
        <!-- 空状态图标 -->
        <view class="empty-icon">
          <image
            class="no-data-image"
            src="../../images/noData.png"
            mode="aspectFit"
          />
        </view>
        <!-- 空状态文字 -->
        <view class="empty-text">当前暂无待办事项~</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Taro, { useDidShow } from "@tarojs/taro";
import { IconFont } from "@nutui/icons-vue-taro";
import { useSelectedStore } from "@/store/modules/selected";

// 设置当前选中的 tab
const selectedStore = useSelectedStore();

// 状态栏高度
const statusBarHeight = ref(44);

// 获取系统信息
useDidShow(() => {
  selectedStore.setSelected(1);
  try {
    Taro.getSystemInfo({
      success: (res) => {
        statusBarHeight.value = res.statusBarHeight || 44;
        console.log("系统信息:", res);
      },
      fail: (err) => {
        console.warn("获取系统信息失败:", err);
        statusBarHeight.value = 44;
      },
    });
  } catch (error) {
    console.warn("获取系统信息异常:", error);
    statusBarHeight.value = 44;
  }
});

// 返回上一页
const goBack = () => {
  selectedStore.setSelected(0);
  try {
    Taro.switchTab({
      url: "/pages/index/index",
    });
  } catch (error) {
    selectedStore.setSelected(1);
    console.warn("返回失败:", error);
  }
};
</script>

<style lang="less">
.find-page {
  height: 100vh;
  position: relative;

  .empty-state {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 60rpx;
    padding-top: 176rpx; // 为自定义头部留出空间
    padding-bottom: 120rpx; // 为底部 TabBar 留出空间

    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .empty-icon {
        .no-data-image {
          width: 240rpx;
          height: 240rpx;
          display: block;
        }
      }

      .empty-text {
        font-size: 24rpx;
        color: #2f3133;
        text-align: center;
        line-height: 40rpx;
        font-weight: 600;
      }
    }
  }
}
</style>
