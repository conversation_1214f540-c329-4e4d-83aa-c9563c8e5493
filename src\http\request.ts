import Taro from '@tarojs/taro'

// 只在开发环境输出调试信息
if (process.env.NODE_ENV === 'development') {
  console.log('NODE_ENV', process.env.NODE_ENV)
  console.log('TARO_APP_PROXY', process.env.TARO_APP_PROXY)
}

const baseUrl = process.env.TARO_APP_PROXY || ''

// HTTP 方法类型
type HttpMethod = 'OPTIONS' | 'GET' | 'HEAD' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'TRACE' | 'CONNECT'

// 请求参数接口
interface RequestParams {
  url: string
  method: HttpMethod
  data?: any
  header?: Record<string, string>
  timeout?: number
  loadingTitle?: string
  toastDuration?: number
  showLoading?: boolean // 是否显示加载提示
}

// 响应数据接口
interface ResponseData<T = any> {
  data?: T
  message?: string
  success: boolean
  statusCode?: number
}
// 请求拦截器 - 添加公共参数
function requestInterceptor(method: HttpMethod, data: any): any {
  if (process.env.NODE_ENV === 'development') {
    console.log('Request method:', method)
    console.log('Request data:', data)
  }

  // 可以在这里添加公共参数
  return data
}

// 防止重复跳转的标志
let isRedirectingToLogin = false

// 处理 401 鉴权失败，跳转到登录页面
function handleAuthError(): void {
  // 防止重复跳转
  if (isRedirectingToLogin) {
    return
  }

  isRedirectingToLogin = true

  // 清除本地存储的 token
  Taro.removeStorageSync('token')

  // 显示提示信息
  Taro.showToast({
    title: '登录已过期，请重新登录',
    icon: 'none',
    duration: 2000
  })

  // 延迟跳转到登录页面，避免与 toast 冲突
  setTimeout(() => {
    // 获取当前页面路径，用于登录后跳转回来
    const pages = Taro.getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentRoute = currentPage?.route || ''

    // 检查当前是否已经在登录页面
    if (currentRoute.includes('login')) {
      isRedirectingToLogin = false
      return
    }

    // 跳转到登录页面，并传递当前页面路径
    Taro.redirectTo({
      url: `/subpackages/login/index?redirect=${encodeURIComponent('/' + currentRoute)}`,
      success: () => {
        // 重置标志，允许下次跳转
        setTimeout(() => {
          isRedirectingToLogin = false
        }, 500)
      },
      fail: () => {
        // 跳转失败时也要重置标志
        isRedirectingToLogin = false
      }
    })
  }, 1000)
}

// 响应拦截器 - 统一处理响应数据
function responseInterceptor(res: any): ResponseData {
  if (res.statusCode === 200) {
    return { ...res.data, success: true, statusCode: res.statusCode }
  } else if (res.statusCode === 401) {
    // 处理 401 鉴权失败
    handleAuthError()
    return {
      message: '登录已过期，请重新登录',
      success: false,
      statusCode: res.statusCode
    }
  } else {
    const errorMessage = res.data?.message?.message || res.data?.message || '请求失败'
    return {
      message: errorMessage,
      success: false,
      statusCode: res.statusCode
    }
  }
}

// 主要的请求函数
export function request<T = any>(params: RequestParams): Promise<ResponseData<T>> {
  const {
    url,
    method,
    data,
    header = {},
    timeout = 6000,
    loadingTitle = '',
    toastDuration = 1500,
    showLoading = true
  } = params

  // 显示加载提示
  if (showLoading && loadingTitle) {
    Taro.showLoading({
      title: loadingTitle,
      mask: true
    })
  }

  // 获取token
  const token = Taro.getStorageSync('token') || ''

  return new Promise<ResponseData<T>>((resolve, reject) => {
    Taro.request({
      data: requestInterceptor(method, data),
      url: baseUrl + url,
      method: method,
      timeout: timeout,
      header: {
        'content-type': 'application/json;charset=UTF-8',
        'X-RD-Request-APIToken': token,
        ...header
      },
      success: (res) => {
        if (showLoading) {
          Taro.hideLoading()
        }

        if (process.env.NODE_ENV === 'development') {
          console.log('Request success:', res)
        }

        const result = responseInterceptor(res)

        if (result.success) {
          resolve(result)
        } else {
          // 如果是 401 错误，不显示额外的错误提示，因为 handleAuthError 已经处理了
          if (result.statusCode !== 401) {
            showError(result.message || '请求失败', toastDuration)
          }
          reject(result)
        }
      },
      fail: (error) => {
        if (showLoading) {
          Taro.hideLoading()
        }

        if (process.env.NODE_ENV === 'development') {
          console.log('Request failed:', error)
        }

        const errorMessage = error.errMsg || '网络请求失败'
        const errorResult: ResponseData<T> = {
          message: errorMessage,
          success: false
        }

        showError(errorMessage, toastDuration)
        reject(errorResult)
      },
      complete: (res) => {
        if (process.env.NODE_ENV === 'development') {
          console.log('Request complete:', res)
        }
      }
    }).catch(error => {
      if (showLoading) {
        Taro.hideLoading()
      }

      if (process.env.NODE_ENV === 'development') {
        console.log('Request catch error:', error)
      }

      const errorMessage = error.errMsg || '请求异常'
      const errorResult: ResponseData<T> = {
        message: errorMessage,
        success: false
      }

      showError(errorMessage, toastDuration)
      reject(errorResult)
    })
  })
}
// 显示错误提示
function showError(message: string, duration = 1500): void {
  Taro.showToast({
    title: message,
    icon: 'none',
    duration: duration
  })
}

// 便捷的 GET 请求方法
export function get<T = any>(url: string, data?: any, options?: Partial<RequestParams>): Promise<ResponseData<T>> {
  return request<T>({
    url,
    method: 'GET',
    data,
    ...options
  })
}

// 便捷的 POST 请求方法
export function post<T = any>(url: string, data?: any, options?: Partial<RequestParams>): Promise<ResponseData<T>> {
  return request<T>({
    url,
    method: 'POST',
    data,
    ...options
  })
}

// 便捷的 PUT 请求方法
export function put<T = any>(url: string, data?: any, options?: Partial<RequestParams>): Promise<ResponseData<T>> {
  return request<T>({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

// 便捷的 DELETE 请求方法
export function del<T = any>(url: string, data?: any, options?: Partial<RequestParams>): Promise<ResponseData<T>> {
  return request<T>({
    url,
    method: 'DELETE',
    data,
    ...options
  })
}
