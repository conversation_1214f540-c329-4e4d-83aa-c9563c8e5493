import { post, get } from '../http/request'

// 收货单列表查询参数
export interface ReceivingListParams {
  page: number
  pageSize: number
  status: 'pending' | 'confirmed' // 待确认/已确认
}

// 收货单项目接口
export interface ReceivingItem {
  id: number // 发货单主键
  deliveryOrderNo: string // 发货单号
  companyName: string // 供应商名称
  orderStatus: string // 单据状态 1待确认发货 2已确认发货 3对方已收货
  deliveryRemark: string // 发货备注
  deliveryRemarkEnabled: boolean // 发货备注开关1开启 0关闭
  remark?: string | null // 收货备注
  remarkSwitch?: boolean | null // 收货备注开关1开启 0关闭
  deliveryOperator: string // 发货作业人
  deliveryTime: string // 发货时间
  receiptOperator?: string // 收货作业人
  // 保留原有字段以兼容现有代码
  shipmentNo?: string // 发货单号（兼容字段）
  customerName?: string // 客户名称（兼容字段）
  materialName?: string // 到货物料（兼容字段）
  shippingTime?: string // 发货时间（兼容字段）
  plateNumber?: string // 车牌号（兼容字段）
  driverPhone?: string // 司机电话（兼容字段）
  licensePlate?: string // 车牌号（兼容字段）
  materialNames?:string // 物料列表
}

// 获取收货单列表
export const getReceivingList = (data: any) => {
  return post('/acc/api/delivery/order/page', data)
}
// 确认收货
export const confirmReceipt = (data: any) => {
  return post(`/acc/api/delivery/receipt`, data)
}
// 查询计划信息
export const queryDeliveryOrderDetail = (data: any) => {
  return get(`/acc/api/delivery/plan/detail?id=${data}`)
}
