export default defineAppConfig({
  darkmode: true, // 所有基础组件均会根据系统主题展示不同的默认样式，navigation bar 和 tab bar 也会根据下面的配置自动切换
  themeLocation: 'theme.json',
  lazyCodeLoading: 'requiredComponents',
  // entryPagePath: 'subpackages/login/index', // 移除固定入口，让应用根据 token 状态自动跳转
  pages: [
    'pages/index/index',
    'pages/find/index',
    'pages/my/index'
  ],
  subpackages: [
    {
      root: 'subpackages',
      pages: [
        'login/index',
        'send/shipEdit/index',
        'send/shipOperation/index',
        'send/shipDetail/index',
        'send/selectPlan/index',
        'send/supplyDetail/index',
        'receiving/receivingOperation/index',
        'receiving/shipmentDetail/index',
        'receiving/detailForm/index',
      ]
    },
  ],
  permission: {
    'scope.userLocation': {
      desc: '你的位置信息将用于小程序位置接口的效果展示'
    },
    "scope.writePhotosAlbum": {
      desc: "将图片保存到相册"
    },
    "scope.camera": {
      desc: "使用相机"
    }
  },
  window: {
    // backgroundColor: '@bgColor', // 窗口的背景色
    backgroundColorTop: '@bgColorTop', // 顶部窗口的背景色，仅 iOS 支持，默认 #ffffff，即loading背景色
    backgroundColorBottom: '@bgColorBottom', // 底部窗口的背景色，仅 iOS 支持，默认 #ffffff
    backgroundTextStyle: '@bgTxtStyle', // 下拉 loading 的样式，仅支持 dark / light，默认 dark
    // navigationStyle: 'custom', // 全局导航栏样式，仅支持以下值：default 默认样式；custom 自定义导航栏，只保留右上角胶囊按钮
    navigationBarBackgroundColor: '@navBgColor', // 导航栏背景颜色，默认 #000000
    navigationBarTitleText: 'WeChat', // 导航栏标题文字内容
    navigationBarTextStyle: '@navTxtStyle' // 导航栏标题颜色，仅支持 black | white
  },
  tabBar: {
    custom: true,
    color: '#AAAAAA',
    selectedColor: '#000000',
    borderStyle: 'black',
    backgroundColor: '#ffffff',
    list: [
      {
        pagePath: 'pages/index/index',
        iconPath: 'images/home_unactive.png',
        selectedIconPath: 'images/home_active.png',
        text: '首页'
      },
      {
        pagePath: 'pages/find/index',
        iconPath: 'images/find_unactive.png',
        selectedIconPath: 'images/find_active.png',
        text: '待办'
      },
      {
        pagePath: 'pages/my/index',
        iconPath: 'images/my_unactive.png',
        selectedIconPath: 'images/my_active.png',
        text: '我的'
      }
    ]
  }
})
