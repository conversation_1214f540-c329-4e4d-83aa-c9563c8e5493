import { defineStore } from 'pinia'
import { ref } from 'vue'

// Setup Store
export const useShipmentStore = defineStore('shipment', () => {
  const detailList = ref<any>([])
  const selectedIds = ref<any>([])
  const selectedPlanNos = ref<any>([])
  function setDetailList(value: any) {
    detailList.value = value
  }
  // 更新明细列表
  function updateDetailList(list: any[]) {
    detailList.value = list
  }

  // 添加明细项
  function addDetailItem(item: any) {
    detailList.value.push(item)
  }

  // 删除明细项
  function removeDetailItem(index: number) {
    let i1 = selectedIds.value.indexOf(detailList.value[index].id)
    if (i1 !== -1) selectedIds.value.splice(i1, 1)
    let i2 = selectedPlanNos.value.indexOf(detailList.value[index].planNo)
    if (i2 !== -1) selectedPlanNos.value.splice(i2, 1)
    detailList.value.splice(index, 1)
  }
  function resetDetailList() {
    detailList.value = []
    selectedIds.value = []
    selectedPlanNos.value = []
  }
  return { detailList, setDetailList, updateDetailList, addDetailItem, removeDetailItem, selectedIds, selectedPlanNos, resetDetailList }
})
