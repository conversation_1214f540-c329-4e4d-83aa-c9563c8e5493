// 全局样式文件
:root,
page {
  height: 100%;
  background: url('./images/all-bg.png') no-repeat center center fixed; // 全局页面整体背景色
  background-size: cover;
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji';
  font-size: 24rpx;
  --nut-primary-color: #1A43AD;
  --nut-primary-color-end: #1A43AD;
  --nut-checkbox-margin-right: 0;
  --nut-form-item-label-font-size: 24rpx;
  --nut-input-font-size: 24rpx;
  --nut-black: #8D9094;
  --nut-textarea-font: 24rpx;
  --nut-textarea-text-color: #8D9094;
  --nut-input-border-bottom: transparent;
}

view {
  box-sizing: border-box;
}

text {
  display: inline-block;
}

image {
  display: inline-block;
  vertical-align: bottom;
}

button {
  &::after {
    border: none;
  }
}

// 自定义头部
.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 22;
  background: transparent;

  .header-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx;

    .header-left {
      width: 120rpx;
      display: flex;
      align-items: center;

      .back-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .back-icon {
          font-size: 48rpx;
          color: #ffffff;
          font-weight: 300;
        }
      }
    }

    .header-center {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      .header-title {
        font-size: 36rpx;
        color: #ffffff;
        font-weight: 500;
      }
    }

    .header-right {
      width: 120rpx;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 16rpx;

      .more-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .more-icon {
          font-size: 32rpx;
          color: #ffffff;
          font-weight: bold;
        }
      }

      .record-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .record-icon {
          width: 32rpx;
          height: 32rpx;
          border-radius: 50%;
          background: #ffffff;
          opacity: 0.9;
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
    opacity: 0.6;
  }

  .empty-text {
    color: #999;
    font-size: 28rpx;
  }
}

// nutui 样式修改
.nut-button--success {
  height: 88rpx !important;
  border-radius: 44rpx !important;
  background: linear-gradient(180deg, #62F8FF 0%, #091899 100%), linear-gradient(180deg, #FFBF1A -33.33%, #E55F14 131.94%) !important;

  /* 橙色投影 */
  box-shadow: 1px 1px 6.1px 0px rgba(243, 198, 106, 0.30) !important;
  color: #FFF !important;

  font-size: 28rpx !important;
  font-style: normal !important;
  font-weight: 600 !important;
  border: none !important;
}

.nut-cell-group__wrap {
  margin: 0 !important;
  box-shadow: none !important;
}

.nut-form-item__label {
  color: #2F3133 !important;
}

.nut-textarea__textarea {
  border-radius: 8px !important;
  background: #F5F7FA !important;
  height: 180rpx !important;
  box-sizing: border-box !important;
  padding: 10rpx !important;
}
