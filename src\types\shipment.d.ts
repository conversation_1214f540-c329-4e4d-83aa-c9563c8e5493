// 发货单相关类型定义

export interface ShipmentDetail {
  deliveryOrderNo: string // 发货单号
  customerCreditCode?: string // 客户统一社会信用代码
  supplierCreditCode?: string // 供应商统一社会信用代码
  customerName: string // 客户名称
  customerPrefix?: string // 客户企业前缀
  supplierName?: string // 供应商名称
  supplierPrefix?: string // 供应商企业前缀
  deliveryAddress: string // 收货地址
  transportType: string // 运输类型
  driverPhone: string // 司机电话
  status: string // 状态 1待确认发货 2已确认发货 3对方已收货
  licensePlate: string // 车牌号
  deliveryRemark?: string // 发货备注
  deliveryRemarkEnabled?: boolean // 发货备注开关
  deliveryOperator?: string // 发货作业人
  deliveryTime?: string // 发货时间
  receiptQuantity?: number // 收货实收数量
  receiptOperator?: string // 收货作业人
  receiptTime?: string // 收货时间
  receiptRemark?: string // 收货备注
  receiptRemarkEnabled?: boolean // 收货备注开关
  categoryCount?: number // 品类数目
  maxDeliveryTime?: string // 最大交货日期
  details: DeliveryOrderDetailVO[] // 发货单明细列表
  deliveryImages?: string[] // 发货图片列表
  receiveImages?: string[] // 收货图片列表
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  detailRows?: number // 明细行数
}

export interface DeliveryOrderDetailVO {
  id?: number // 主键ID
  deliveryOrderNo?: string // 发货单号
  planNo?: string // 计划编号
  materialCode?: string // 物料编码
  materialName?: string // 物料名称
  planQuantity?: number // 计划数量
  actualQuantity?: number // 实发数量
  unit?: string // 单位
  unitName?: string // 单位名称
  planDeliveryTime?: string // 计划交付时间
  [key: string]: any // 允许其他字段
}

// 保留原有接口以兼容现有代码
export interface ShipmentDetailItem {
  planNo: string
  materialName: string
  planQuantity: string
  actualQuantity: string
  planTime: string
}

export interface ShippingInfo {
  address: string
  transportType: string
  driverPhone: string
  plateNumber: string
  remarks: string
}

export interface ShipmentListItem {
  shipmentNo: string
  customerName: string
  address: string
  productCount: number
  daysRemaining: number
  status?: 'pending' | 'confirmed'
}
