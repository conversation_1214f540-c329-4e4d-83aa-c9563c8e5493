<template>
  <view class="receiving-page">
    <!-- Tab切换 -->
    <view style="padding: 0 100rpx">
      <view class="tab-container">
        <view
          class="tab-item"
          :class="{ active: activeTab === 0 }"
          @tap="switchTab(0)"
          >待确认收货</view
        >
        <view
          class="tab-item"
          :class="{ active: activeTab === 1 }"
          @tap="switchTab(1)"
          >已确认收货</view
        >
      </view>
    </view>

    <!-- 列表内容 -->
    <view class="receiving-list-panel">
      <scroll-view
        scroll-y="true"
        class="receiving-list"
        @scrolltolower="loadMore"
        lower-threshold="200"
        refresher-enabled="true"
        :refresher-triggered="refreshing"
        @refresherrefresh="onRefresh"
      >
        <view
          class="receiving-card"
          v-for="(item, index) in receivingList"
          :key="index"
          @tap="goToDetail(item.deliveryOrderNo)"
        >
          <view class="receiving-header">
            <view class="receiving-title">{{
              item.companyName || item.customerName
            }}</view>
            <view class="sender-info" v-if="!!item.deliveryRemarkEnabled">
              发货方备注
            </view>
          </view>
          <view class="receiving-number">
            <text class="label">发货单号</text>
            <text class="value">{{
              item.deliveryOrderNo || item.shipmentNo
            }}</text>
          </view>
          <view class="divider"></view>
          <view class="receiving-info">
            <view class="info-item">
              <text class="label">到货物料：</text>
              <text
                class="value"
                v-if="item.materialNames && item.materialNames.length === 1"
                >{{ item.materialNames[0] }}</text
              >
              <text
                v-if="item.materialNames && item.materialNames.length > 1"
                class="value"
                >{{ item.materialNames[0] }}等{{
                  item.materialNames.length
                }}项</text
              >
            </view>
            <view class="info-item">
              <text class="label">发货时间：</text>
              <text class="value">{{ item.deliveryTime }}</text>
            </view>
            <view class="info-item">
              <text class="label">车牌号：</text>
              <text class="value">{{ item.licensePlate }}</text>
              <text class="phone-label">司机电话：</text>
              <text class="phone-value">{{ item.driverPhone }}</text>
            </view>
          </view>
        </view>

        <view class="loading" v-if="loading">加载中...</view>
        <view
          class="no-more"
          v-if="!hasMore && !loading && receivingList.length > 0"
          >没有更多数据</view
        >
        <view class="empty-state" v-if="!loading && receivingList.length === 0">
          <image
            class="empty-icon"
            src="../../../images/noData.png"
            mode="aspectFit"
          ></image>
          <view class="empty-text">暂无数据</view>
        </view>
        <!-- 添加底部空间，确保可以滚动到底部 -->
        <view class="bottom-space"></view>
      </scroll-view>
    </view>

    <!-- 扫码收货按钮 -->
    <view class="scan-button">
      <view class="scan-button-icon" @tap="handleScan">
        <image
          style="width: 200rpx; height: 200rpx"
          src="../../../images/saomashou.png"
          mode="aspectFit"
        ></image>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import Taro from "@tarojs/taro";
import { getReceivingList, type ReceivingItem } from "@/api/receiving";

const userInfo = JSON.parse(Taro.getStorageSync("userInfo"));

// 当前激活的tab
const activeTab = ref(0);
// 列表数据
const receivingList = ref<ReceivingItem[]>([]);
// 分页参数
const page = ref(1);
const pageSize = ref(10);
const loading = ref(false);
const hasMore = ref(true);
const refreshing = ref(false);
const status = ref("");

// 切换tab
const switchTab = (index: number) => {
  if (activeTab.value === index) return;
  activeTab.value = index;
  // 重置分页
  page.value = 1;
  receivingList.value = [];
  hasMore.value = true;
  fetchReceivingList();
};

// 获取收货单列表
const fetchReceivingList = async () => {
  if (loading.value || !hasMore.value) return;
  loading.value = true;

  try {
    const orderStatus = activeTab.value === 0 ? "2" : "3";
    status.value = orderStatus;
    const res = await getReceivingList({
      pageNum: page.value,
      pageSize: pageSize.value,
      orderStatus,
    });

    console.log("API响应数据:", res);

    // 处理API响应数据
    if (res && res.success && res.data) {
      // 根据实际API响应结构处理数据
      // 如果API返回的是分页数据结构 { list: [], total: number, pageNum: number, size: number }
      if (res.data.list && Array.isArray(res.data.list)) {
        const { list = [], total = 0, pageNum = 1 } = res.data;

        // 更新列表数据
        if (page.value === 1) {
          receivingList.value = list;
        } else {
          receivingList.value = [...receivingList.value, ...list];
        }

        // 判断是否还有更多数据
        const totalLoaded = receivingList.value.length;
        hasMore.value = totalLoaded < total;

        // 页码递增
        page.value++;

        console.log(
          `加载完成，当前页码：${pageNum}，当前总数：${totalLoaded}/${total}，还有更多：${hasMore.value}`
        );
      }
      // 如果API直接返回数组
      else if (Array.isArray(res.data)) {
        if (page.value === 1) {
          receivingList.value = res.data;
        } else {
          receivingList.value = [...receivingList.value, ...res.data];
        }

        // 如果返回的数据少于请求的pageSize，说明没有更多数据了
        hasMore.value = res.data.length >= pageSize.value;
        page.value++;

        console.log(
          `加载完成，当前页码：${page.value - 1}，本次加载：${
            res.data.length
          }条，还有更多：${hasMore.value}`
        );
      } else {
        // 数据格式不符合预期
        if (page.value === 1) {
          receivingList.value = [];
        }
        hasMore.value = false;
        console.log("API返回数据格式不符合预期:", res.data);
      }
    } else {
      // API调用失败或无数据
      if (page.value === 1) {
        receivingList.value = [];
      }
      hasMore.value = false;
      console.log("API调用失败或无数据:", res);

      // 显示API返回的错误信息
      if (res && res.message) {
        Taro.showToast({
          title: res.message,
          icon: "none",
        });
      }
    }
  } catch (error) {
    console.error("获取发货单列表失败", error);
    Taro.showToast({
      title: "获取数据失败",
      icon: "none",
    });
    // 发生错误时，如果是第一页，清空列表
    if (page.value === 1) {
      receivingList.value = [];
    }
    hasMore.value = false;
  } finally {
    loading.value = false;
  }
};

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loading.value) {
    fetchReceivingList();
  }
};

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true;
  try {
    // 重置分页状态
    page.value = 1;
    hasMore.value = true;

    // 重新获取数据
    await fetchReceivingList();
  } finally {
    refreshing.value = false;
  }
};

// 跳转到收货单详情页面
const goToDetail = (deliveryOrderNo: string | undefined) => {
  Taro.navigateTo({
    url: `/subpackages/receiving/shipmentDetail/index?deliveryOrderNo=${deliveryOrderNo}&orderStatus=${status.value}`,
  });
};

// 扫码功能
const handleScan = () => {
  Taro.scanCode({
    success: (res) => {
      // dlbnb-供应商entcode-客户entcode-88.588.000071/FH00006920250623017
      console.log("扫码结果", res, userInfo.entCode);
      const arr = res.result.split("-");
      if (arr.includes("dlbnb")) {
        if (arr[2] !== userInfo.entCode) {
          return Taro.showToast({
            title: "没有权限访问该收货单",
            icon: "none",
          });
        }
        Taro.navigateTo({
          url: `/subpackages/send/shipEdit/index?deliveryOrderNo=${
            arr[arr.length - 1]
          }&orderStatus=${2}`,
        });
        Taro.showToast({
          title: "扫码成功",
          icon: "none",
          duration: 1000,
        });
      } else {
        Taro.showToast({
          title: "二维码不正确",
          icon: "none",
        });
      }
    },
    fail: (err) => {
      console.error("扫码失败", err);
      // Taro.showToast({
      //   title: "扫码失败",
      //   icon: "none",
      // });
    },
  });
};

const getMaterialNames = (item: any) => {
  if (!!item && item.length > 0) {
    console.log("item", item);
    return item.join(",");
  }
};

onMounted(() => {
  fetchReceivingList();
});
</script>

<style lang="less">
.receiving-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 0 30rpx 30rpx 30rpx;
  box-sizing: border-box;
}

.receiving-list-panel {
  margin-top: 30rpx;
  flex: 1;
  overflow-y: auto;
}

.receiving-list {
  height: 100%;
}

.tab-container {
  height: 68rpx;
  border-radius: 34rpx;
  background: #fff;

  /* 下层投影 */
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-sizing: border-box;
  margin-top: 20rpx;

  .tab-item {
    flex: 1;
    height: 44rpx;
    line-height: 44rpx;
    text-align: center;
    font-size: 28rpx;
    color: #8d9094;
    position: relative;

    &.active {
      color: #303030;
      font-weight: 500;

      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 50%;
        transform: translateX(-50%);
        height: 2px;
        background-color: #4068f5;
      }
    }
  }
}
.bottom-space {
  height: 250rpx; /* 为底部扫码按钮预留空间，确保可以滚动到底部 */
}
.receiving-card {
  border-radius: 16rpx;
  background: #fff;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.12);
  padding: 20rpx;
  margin-bottom: 20rpx;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .receiving-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
  }

  .receiving-title {
    font-size: 28rpx;
    color: #1a43ad;
    font-weight: 600;
  }

  .sender-info {
    font-size: 24rpx;
    color: #4facfe;
    background: #e6f7ff;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
  }

  .receiving-number {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .label {
      font-size: 28rpx;
      color: #8d9094;
    }

    .value {
      font-size: 28rpx;
      font-weight: 400;
      color: #000;
      white-space: nowrap; /* 禁止换行 */
      overflow: hidden; /* 隐藏溢出部分 */
      text-overflow: ellipsis; /* 超出时显示省略号 */
      max-width: 80%; /* 根据实际情况设置最大宽度 */
    }
  }

  .divider {
    height: 1px;
    background-color: #ced4db;
    margin: 20rpx 0;
  }

  .receiving-info {
    .info-item {
      margin-bottom: 12rpx;
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      .label {
        color: #8d9094;
        font-size: 24rpx;
      }

      .value {
        color: #2f3133;
        font-size: 24rpx;
      }

      .phone-label {
        color: #8d9094;
        font-size: 24rpx;
        margin-left: 40rpx;
      }

      .phone-value {
        color: #2f3133;
        font-size: 24rpx;
      }
    }
  }
}

.loading,
.no-more {
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 24px;
}

.scan-button {
  width: 100vw;
  height: 300rpx;
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  background: url("../../../images/Union.png") no-repeat;
  background-size: cover;
  .scan-button-icon {
    position: absolute;
    top: 38%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
