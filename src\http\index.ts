import { request } from './request'

export function getAction(url: string, parameter = {}) {
  return request({
    url: url,
    method: 'GET',
    data: parameter,
  })
}
export function postAction(url: string, parameter = {}) {
  return request({
    url: url,
    method: 'POST',
    data: parameter,
    // header: {
    //   'Content-Type': 'application/x-www-form-urlencoded'
    // }
  })
}

export function postFormAction(url: string, parameter = {}) {
  return request({
    url: url,
    method: 'POST',
    data: parameter,
    header: {
      'content-type': 'multipart/form-data; boundary=----WebKitFormBoundaryOyG4QBzD4onVMExj'
    }
  })
}
