// 收货单相关类型定义

export interface ReceivingDetail {
  shipmentNo: string
  customerName: string
  organization: string
  details: ReceivingDetailItem[]
  shippingInfo: ReceivingShippingInfo
  images: string[]
}

export interface ReceivingDetailItem {
  planNo: string
  materialName: string
  planQuantity: string
  actualQuantity: string
  shippingTime: string
}

export interface ReceivingShippingInfo {
  shippingTime: string
  transportType: string
  driverPhone: string
  plateNumber: string
  remarks: string
}

export interface ReceivingListItem {
  shipmentNo: string
  customerName: string
  materialName: string
  shippingTime: string
  plateNumber: string
  driverPhone: string
  status?: 'pending' | 'confirmed'
}

export interface ReceivingListParams {
  page: number
  pageSize: number
  status: 'pending' | 'confirmed' // 待确认/已确认
}
