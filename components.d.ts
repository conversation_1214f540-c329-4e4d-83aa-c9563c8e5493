/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ImageUploader: typeof import('./src/components/ImageUploader/index.vue')['default']
    Login: typeof import('./src/components/Login.vue')['default']
    NutButton: typeof import('@nutui/nutui-taro')['Button']
    NutCheckbox: typeof import('@nutui/nutui-taro')['Checkbox']
    NutCol: typeof import('@nutui/nutui-taro')['Col']
    NutInput: typeof import('@nutui/nutui-taro')['Input']
    NutPopup: typeof import('@nutui/nutui-taro')['Popup']
    NutRow: typeof import('@nutui/nutui-taro')['Row']
    NutSwipe: typeof import('@nutui/nutui-taro')['Swipe']
    NutSwitch: typeof import('@nutui/nutui-taro')['Switch']
    NutTextarea: typeof import('@nutui/nutui-taro')['Textarea']
    NutUploader: typeof import('@nutui/nutui-taro')['Uploader']
  }
}
