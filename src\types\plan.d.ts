// 供货计划相关类型定义

export interface SupplyPlan {
  id: string
  planCode: string
  materialName: string
  materialSpec: string
  planQuantity: string
  planDate: string
  remark?: string
  unitName?: string
  planDeliveryTime: any
  status: 'urgent' | 'warning' | 'normal'
  remarkSwitch: any
}

export interface CustomerInfo {
  customerName: string
  enterpriseCode: string
  customerEntCode: string
}

export interface SelectPlanParams {
  customerId?: string
  customerName?: string
  enterpriseCode?: string
  customerEntCode: string
}
