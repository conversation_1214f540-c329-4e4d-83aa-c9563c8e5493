// src/store/modules/receivingForm.ts
import { defineStore } from 'pinia'
// import type { ReceivingFormItem } from '@/types/receiving'
import { ref } from "vue";

export interface ReceivingFormItem {
  planNo: string
  receiptQuantity: string | null | number
}

export const useReceivingFormStore = defineStore('receivingForm', () => {
  const formItems = ref<any>([])

  function setFormItem(item: ReceivingFormItem) {
    // 检查是否已经存在相同的 planNo
    const existingItemIndex = formItems.value.findIndex((i: any) => i.planNo === item.planNo);
    if (existingItemIndex !== -1) {
      // 如果存在，更新该项
      formItems.value[existingItemIndex] = item;
      return;
    }
    formItems.value.push(item);
  }

  function getFormItem(planNo: string): ReceivingFormItem | null {
    console.log(planNo, formItems.value, 'formItems.value');

    const index = formItems.value.findIndex((item) => item.planNo === planNo);
    if (index !== -1) {
      return formItems.value[index];
    }
    return null;
  }

  function clearFormItems() {
    formItems.value = []
  }

  return { formItems, setFormItem, getFormItem, clearFormItems }
})
