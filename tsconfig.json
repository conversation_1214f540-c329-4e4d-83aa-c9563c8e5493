{"compilerOptions": {"target": "es2017", "module": "commonjs", "removeComments": false, "preserveConstEnums": true, "moduleResolution": "node", "experimentalDecorators": true, "noImplicitAny": false, "allowSyntheticDefaultImports": true, "outDir": "lib", "noUnusedLocals": true, "noUnusedParameters": true, "strictNullChecks": true, "sourceMap": true, "baseUrl": ".", "rootDir": ".", "jsx": "preserve", "allowJs": true, "resolveJsonModule": true, "typeRoots": ["node_modules/@types"], "paths": {"@/*": ["src/*"]}}, "include": ["./src", "./types", "./config", "components.d.ts"], "compileOnSave": false}